import { TypebotService } from './../service/ia/TypebotService';
// noinspection LanguageDetectionInspection

import {MapeadorDeTaxaDeEntregaCalculada} from './../mapeadores/MapeadorDeTaxaDeEntregaCalculada';
import {Router} from "express";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {Contato} from "../domain/Contato";
import {EnumStatusContato} from "../lib/emun/EnumStatusContato";
import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeFoto} from "../mapeadores/MapeadorDeFoto";
import {Foto} from "../domain/Foto";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {NotificacaoService} from "../service/NotificacaoService";
import {Ambiente} from "../service/Ambiente";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import * as request from "request-promise-native";
import {SituacaoDeMensagem} from "../service/SituacaoDeMensagem";
import {CodigoVerificacao} from "../domain/CodigoVerificacao";
import {MapeadorDeCodigoDeVerificacao} from "../mapeadores/MapeadorDeCodigoDeVerificacao";
import {MapeadorDeCartao} from "../mapeadores/MapeadorDeCartao";
import {MapeadorDePlano} from "../mapeadores/MapeadorDePlano";
import {RegraUlils} from "../utils/RegraUtils";
import {DadosRelatorioClientesFidelidade} from "../domain/relatorios/DadosRelatorioClientesFidelidade";
import {RelatorioClientesFidelidade} from "../domain/relatorios/RelatorioClientesFidelidade";
import * as soap from 'soap';
import {PlanoEmpresarial} from "../domain/faturamento/PlanoEmpresarial";
import {StatusDeMensagem} from "../service/StatusDeMensagem";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import * as moment from 'moment';
import {MapeadorDeAcaoDoContato} from "../mapeadores/MapeadorDeAcaoDoContato";
import {DTOAcaoContato} from "../lib/dto/DTOAcaoContato";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import {DTOPontuacaoVencer} from "../lib/dto/DTOPontuacaoVencer";
import {MapeadorDeEstado} from "../mapeadores/MapeadorDeEstado";
import {MapeadorDeCidade} from "../mapeadores/MapeadorDeCidade";
import {PedidoService} from "../service/PedidoService";
import {ContatoService} from "../service/ContatoService";
import {TomtomService} from "../service/TomtomService";
import {Endereco} from "../domain/delivery/Endereco";
import {MapeadorDeEndereco} from "../mapeadores/MapeadorDeEndereco";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {DTOContrato} from "../lib/dto/DTOContrato";
import {CepService} from "../service/CepService";
import {FormaDeEntregaEmpresa} from "../domain/delivery/FormaDeEntregaEmpresa";
import {ApiCloudfare} from "../lib/cloudfare/ApiCloudfare";
import {Usuario} from "../domain/Usuario";

import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {Notificacao} from "../domain/Notificacao";
//nao retirar o import, faz compilar a classe
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {MapeadorDeMesa} from "../mapeadores/MapeadorDeMesa";
import {MapeadorDeSessaoMesa} from "../mapeadores/MapeadorDeSessaoMesa";
import {SessaoMesa} from "../domain/SessaoMesa";
import {GerenciadorDeAssinantesSSE} from "../service/GerenciadorDeAssinantesSSE";
import {Produto} from "../domain/Produto";
import {Brinde} from "../domain/obj/Brinde";
import {ClienteApi} from "../domain/api/ClienteApi";
import * as passport from "passport";
import {MapeadorDeClienteApi} from "../mapeadores/MapeadorDeClienteApi";
import {BearerToken} from "../domain/api/BearerToken";
import {MapeadorDeBearerToken} from "../mapeadores/MapeadorDeBearerToken";
import {EmpresaService} from "../service/EmpresaService";
import {ProdutoService} from "../service/ProdutoService";
import {MapeadorDeBanner} from "../mapeadores/MapeadorDeBanner";
import {EnumStatusCampanha} from "../domain/EnumStatusCampanha";
import {MapeadorDeCampanha} from "../mapeadores/MapeadorDeCampanha";
import {CacheService} from "../service/CacheService";
import {TokenGcm} from "../domain/app/TokenGcm";
import {MapeadorDeTokenGcm} from "../mapeadores/MapeadorDeTokenGcm";
import {ChinaInBoxService} from "../service/ChinaInBoxService";
import {Modulo} from "../domain/Modulo";
import {TaxaDeEntregaCalculada} from "../domain/delivery/TaxaDeEntregaCalculada";
//import {BuscadorDeFotosEANService} from "../service/baseDeFotosEAN/BuscadorDeFotosEANService";
import {IuguService} from "../service/IuguService";
import {DTOFatura} from "../lib/dto/DTOFatura";
import {ContratoService} from "../service/ContratoService";
import {AssinadorDeXml} from "../utils/nfse/AssinadorDeXml";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {NotaFiscalDeServico} from "../domain/faturamento/NotaFiscalDeServico";
import {InvocadorSoapNfseGoiania} from "../utils/nfse/InvocadorSoapNfseGoiania";
import {EnumStatusFatura} from "../lib/emun/EnumStatusFatura";
import {TarefaDeEnvioDeNfse} from "../domain/faturamento/TarefaDeEnvioDeNfse";
import {RespostaFazEntregaKml} from "../lib/taxaDeEntrega/RespostaFazEntregaKML";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {MapeadorDeUsuario} from "../mapeadores/MapeadorDeUsuario";
import {DiaDaSemanaEnumLabel} from "../domain/DiaDaSemanaEnum";
import {MapeadorDeGrupoDeLojas} from "../mapeadores/MapeadorDeGrupoDeLojas";
import {DominioDaEmpresa} from "../domain/DominioDaEmpresa";
import {SessaoLinkSaudacao} from "../domain/SessaoLinkSaudacao";
import {MapeadorDeSessaoLinkSaudacao} from "../mapeadores/MapeadorDeSessaoLinkSaudacao";
import {Contrato} from "../domain/faturamento/Contrato";
import {Localizacao} from "../utils/Localizacao";
import {GrupoDeLojas} from '../domain/GrupoDeLojas';
import {GrupoDeLojasService} from '../service/GrupoDeLojasService';
import {MapeadorDeCategoria} from '../mapeadores/MapeadorDeCategoria';
import {MapeadorDeVitrine} from "../mapeadores/MapeadorDeVitrine";
import {MapeadorDeAdicionalDeProduto} from "../mapeadores/MapeadorDeAdicionalDeProduto";
import {MapeadorDeProdutoTemplateAdicional} from "../mapeadores/MapeadorDeProdutoTemplateAdicional";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {EnumPaginaGrupoDirecionar} from "../domain/EnumPaginaGrupoDirecionar";
import {TratadorDeMensagemWitai} from "../lib/instagram/TratadorDeMensagemWitai";
import {LinkEncurtado} from "../domain/LinkEncurtado";
import {AvisoDeSistema} from "../domain/avisos/AvisoDeSistema";
import {TipoDeAvisoEnum} from "../domain/avisos/TipoDeAvisoEnum";
import {ProdutoEmbeddings} from "../domain/ProdutoEmbeddings";
import {PhoneNumberType, PhoneNumberUtil} from "google-libphonenumber";
import {FactoryIntegracaoDelivery} from "../domain/integracoes/FactoryIntegracaoDelivery";
import {GcomERPService} from "../service/integracoes/GcomERPService";
import {MapeadorDeMensagemBot} from "../mapeadores/MapeadorDeMensagemBot";
import {Banner} from "../domain/delivery/Banner";
import {MensagemBot} from "../domain/MensagemBot";
import {MapeadorDeProdutoEmbeddings} from "../mapeadores/MapeadorDeProdutoEmbeddings";
import {EnumTemas} from "../domain/temas/EnumTemas";
import {EnumServicoHorarioFuncionamento} from "../lib/emun/EnumServicoHorarioFuncionamento";
import {CategoriaProdutosUtils} from "../lib/CategoriaProdutosUtils";
import {EnumMeioDePagamento} from "../domain/delivery/EnumMeioDePagamento";
import {JSONUtils} from "../utils/JSONUtils";

const QRCode = require('qrcode')
const router: Router = Router();
const async = require('async');
const totalVoice = require('totalvoice-node');
const randtoken = require('rand-token');
const _  = require('underscore');
const axios = require('axios');
const qs = require('qs');
const crypto = require('crypto');
import bcrypt = require('bcrypt');
//import cupom
import {Cupom} from "../domain/faturamento/Cupom";


let redis = require("redis");
let publisher = redis.createClient();
//publisher.select(1);
let client = redis.createClient();

import uuid = require("uuid");
import {Token} from "../domain/Token";
import {MepeadorDeTagProduto} from "../mapeadores/MepeadorDeTagProduto";
import {TagProdutoTipoEnum} from "../domain/TagProduto";
import {ChatGPTService} from "../service/ia/ChatGPTService";
import {OpenRouterGTPService} from "../service/ia/OpenRouterGTPService";
import {MapeadorDeConfiguracoesMia} from "../mapeadores/MapeadorDeConfiguracoesMia";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {Catalogo} from "../domain/catalogo/Catalogo";
import {CatalogoDaRede} from "../domain/catalogo/CatalogoDaRede";
import {MapeadorDeCatalogoDaRede} from "../mapeadores/MapeadorDeCatalogoDaRede";
import {MapeadorDeDisponibilidade} from "../mapeadores/MapeadorDeDisponibilidade";
import {MapeadorDeBanco} from "../mapeadores/MapeadorDeBanco";
import { BitrixServiceFactory } from '../service/bitrix/BitrixService';
import {Lead} from '../domain/crm/Lead';
import { OrigemLead } from '../domain/crm/LeadEnums';
import MapeadorDeLead from '../mapeadores/MapeadorDeLead';
import { CrmEmpresa } from '../domain/crm/CrmEmpresa';
import MapeadorDeCrmEmpresa from '../mapeadores/MapeadorDeCrmEmpresa';
import {LeadLink, TipoLinkLead} from "../domain/crm/LeadLink";


let oauth2orize = require('oauth2orize');
let server = oauth2orize.createServer();



server.exchange(oauth2orize.exchange.clientCredentials( (clienteApi: any, scope: any, done: any) => {
  console.log('Validando cliente pela credencial')
  const c: CrmEmpresa = new CrmEmpresa();
  new MapeadorDeClienteApi().selecioneSync(clienteApi.id).then((cliente) => {
    if(!cliente){
      console.log('Cliente não encontrado')
      return done(null, false, {mensagem: 'Cliente não encontrado'});
    }
    if (cliente.segredo !== clienteApi.segredo) {
      console.log('Segredo do cliente inválido')
      return done(null, false, {mensagem: 'Segredo do cliente inválido'});
    }

    //Pass in a null for user id since there is no user with this grant type
    let token = new BearerToken(cliente);

    new MapeadorDeBearerToken().insiraGraph(token).then((idToken: any) => {
      done(null, token.getToken())
    })
  });
}));


moment.locale('pt-br');

router.post('/registre', (req, res) => {
  //Criar um novo 'cliente' para API. Serve para criar um clientId e clientSecrete para um novo app que vai se integrar
  let nome = req.body.nome
  let identificador = req.body.identificador
  let tipo = req.body.tipo
  let cabecalho = req.headers

  if(!nome)
    return res.json(Resposta.erro('É necessário informar o nome do aplicativo que irá consumir a API'))

  if(!identificador)
    return res.json(Resposta.erro('É necessário informar um identificador único para o aplicativo que irá consumir a API'))


  let ip: string = cabecalho && cabecalho['x-forwarded-for'] && cabecalho['x-forwarded-for'].length > 0  ?
    cabecalho['x-forwarded-for'][0]  :  req.connection.remoteAddress;


  let cliente = new ClienteApi(nome, tipo, identificador, ip)

  cliente.gereIdESegredo();

  new MapeadorDeClienteApi().insiraGraph(cliente).then((idCliente: any) => {
    if(!idCliente)
      return res.json(Resposta.erro('Houve um erro ao salvar o novo cliente. Tente novamente.'))

    return res.json(Resposta.sucesso(cliente.obtenhaDTO()))
  }).catch((reason: any) => {
    res.json(Resposta.erro("Houve um erro ao criar o cliente: " + reason))
  })
})

router.post('/token', [
  passport.authenticate(['clientBasic', 'clientPassword'], { session: false }),
  server.token(),
  server.errorHandler()

])

router.get('/buscarSMSTotalVoice', (req, res) => {
  const msg = req.query.m;

  const cliente = new totalVoice("621d9ba7a78af6bc5fc1c0c15ce1984d");

  cliente.sms.buscar('51134723', msg)
    .then(function (data: any) {
      console.log(data)
      res.json(data);
    })
    .catch(function (error: any) {
      console.error('Erro: ', error)
    });
});


router.get('/testeSMSTotalVoice2', (req, res) => {
  const msg = req.query.m;

  const cliente = new totalVoice("621d9ba7a78af6bc5fc1c0c15ce1984d");

  cliente.sms.enviar('62982301144', msg)
    .then(function (data: any) {
      console.log(data)
    })
    .catch(function (error: any) {
      console.error('Erro: ', error)
    });
});

router.post('/mudeStatus', (req: any, res) => {
  console.log('mude status');
  console.log(req.query);

  const idMensagem = req.query.idm;
  const novoStatus = req.query.s;

  console.log(idMensagem);

  if (!idMensagem) {
    return res.json(Resposta.erro('Parametros invalidos'));
  }

  const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

  mapeadorDeMensagemEnviada.selecioneSync({id: idMensagem}).then((mensagemObtida: MensagemEnviada) => {
    if (!mensagemObtida) {
      return res.json(Resposta.erro('Parametros invalidos'));
    }

    mensagemObtida.status = novoStatus;

    mapeadorDeMensagemEnviada.atualizeSync(mensagemObtida).then( (sucesso: any) => {
      res.json({
        sucesso: true
      });
    });
  });
});

router.get('/notificar', (req, res) => {
  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const objeto = {tipo: 'enviou-mensagens', id: 84721};

    publisher.publish(empresa.id, JSON.stringify(objeto));

    publisher.publish(empresa.id, JSON.stringify(objeto));
    res.json({
      sucesso: true
    });
  });
});

router.get('/teste', async (req: any, res) => {
  if( true ) {
    return res.json(Resposta.sucesso(true));
  }

  return res.json({
    sucesso: true,
    senha: bcrypt.hashSync('123456', 12)
  })

  const data = qs.stringify({
    'paymentMode': 'default',
    'paymentMethod': 'creditCard',
    'receiverEmail': '<EMAIL>',
    'currency': 'BRL',
    'extraAmount': '0.00',
    'itemId1': '0001',
    'itemDescription1': 'NotebookPrata',
    'itemAmount1': '7.00',
    'itemQuantity1': '1',
    'notificationURL': 'https://sualoja.com.br/notifica.html',
    'reference': 'REF1234',
    'senderName': 'Fulano de Tal',
    'senderCPF': '00114011176',
    'senderAreaCode': '11',
    'senderPhone': '56273440',
    'senderEmail': '<EMAIL>',
    'senderHash': '8ee553e1e94b164710c32286f49d827eb09806339804226fa409d8a8ee307bbd',
    'shippingAddressStreet': 'Av.Brig.FariaLima',
    'shippingAddressNumber': '1384',
    'shippingAddressComplement': '5oandar',
    'shippingAddressDistrict': 'JardimPaulistano',
    'shippingAddressPostalCode': '01452002',
    'shippingAddressCity': 'SaoPaulo',
    'shippingAddressState': 'SP',
    'shippingAddressCountry': 'BRA',
    'shippingType': '1',
    'shippingCost': '0.00',
    'creditCardToken': '3ea303bb706a44c295e108ae49746ca4',
    'installmentQuantity': '1',
    'installmentValue': '7.00',
    'noInterestInstallmentQuantity': '2',
    'creditCardHolderName': 'Fulano de Tal',
    'creditCardHolderCPF': '00114011176',
    'creditCardHolderBirthDate': '31/03/1983',
    'creditCardHolderAreaCode': '11',
    'creditCardHolderPhone': '56273440',
    'billingAddressStreet': 'Av.Brig.FariaLima',
    'billingAddressNumber': '1384',
    'billingAddressComplement': '5oandar',
    'billingAddressDistrict': 'JardimPaulistano',
    'billingAddressPostalCode': '01452002',
    'billingAddressCity': 'SaoPaulo',
    'billingAddressState': 'SP',
    'billingAddressCountry': 'BRA'
  });
  const config = {
    method: 'post',
    url: 'https://ws.sandbox.pagseguro.uol.com.br/v2/transactions?email=<EMAIL>' +
      '&token=8EC5C97C41B046EAA7ACFA30DE7C5C7F',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data : data
  };

  axios(config)
    .then(function (response: any) {
      console.log(JSON.stringify(response.data));
      res.json({
        sucesso: true,
        data: response.data
      })
    })
    .catch(function (error: any) {
      console.log(error.response.data);
      res.json({
        sucesso: false,
        data: error.response.data
      })
    });
});

router.get('/cliente/email/existe', async(req, res) => {
  const email = req.query.email;

  if(!email) return res.json(Resposta.sucesso())

  let existe = await new MapeadorDeContato().existeSync({email: email})

  res.json(Resposta.sucesso(existe > 0))
});

async function gereAviso(req: any, res: any, tipoDeAviso: TipoDeAvisoEnum) {
  const id = Number(req.params.id);
  const empresa: Empresa = req.empresa;

  if(!id)
    return res.json(Resposta.erro("O id " + id + " não é um número válido"))

  let mesa = await (new MapeadorDeMesa()).selecioneSync({id: id})

  if(!mesa)
    return res.json(Resposta.erro("Não foi encontrada mesa com o id " + id))

  let aviso = tipoDeAviso === TipoDeAvisoEnum.SolicitacaoDeGarcom ?
    AvisoDeSistema.AvisoSolicitacaoDeGarcom(empresa,  mesa) :
    AvisoDeSistema.AvisoFecharConta(empresa,  mesa)

  await aviso.salve(true)

  return res.json(Resposta.sucesso(aviso))
}

router.post('/mesas/solicite-fechar-mesa/:id', async (req: any, res: any) => {
  return await gereAviso(req, res, TipoDeAvisoEnum.FecharConta)
})


router.post('/mesas/solicite-garcom/:id', async (req: any, res: any) => {
  return await gereAviso(req, res, TipoDeAvisoEnum.SolicitacaoDeGarcom)
});

router.get('/telefone/:telefone', (req, res) => {
  const tel = req.params.telefone;

  if( !tel ) {
    res.json({
      sucesso: false,
      mensagem: 'Telefone é obrigatório'
    })
  }

  const mapeadorDeContato = new MapeadorDeContato();

  console.log('telefone: ' + tel);

  mapeadorDeContato.selecioneSync({telefone: tel}).then( (contato: Contato) => {
    const dados: any = contato;

    dados.link = new VariaveisDeRequest().obtenhaUrlRaiz(contato.empresa) + contato.obtenhaLinkCartaoNaoAtivado();

    res.json({
      sucesso: contato != null,
      cartao: dados,
      mensagem: 'ok'
    });
  });
});

router.get('/mensagens/ultima', (req: any, res: any) => {
  const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();
    const usuario: Usuario = req.user;

    if( !usuario ) {
      return res.json({
        sucesso: true,
        data: {
          id: -1
        }
      });
    }

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    let numeroWhatsapp = null;

    //TODO: Após alguns dias remover esse código pq usuários logados já virão sempre com o número whatsapp
    if (!numeroWhatsapp) {
      numeroWhatsapp = empresa.numeroWhatsapp;
    }

    mapeadorDeMensagemEnviada.listeAsync({
      ultimaMensagem: true,
      inicio: 0,
      numeroWhatsapp: numeroWhatsapp,
      total: 1
    }).then(async (mensagens) => {
      console.log('mensagens: ' + mensagens.length);

      if (mensagens.length === 0) {
        return res.json({
          sucesso: true,
          data: {
            id: -1
          }
        });
      }

      res.json({
        sucesso: true,
        data: {
          id: mensagens[0].id - 1
        }
      });
    });
  });
});

router.get('/horarioServidor', (req, res) => {
  res.json({
    sucesso: true,
    horario: new Date()
  });
});

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

router.get('/grupolojas/me', async (req: any, res) => {
  let grupoDeLojas: any = req.grupoDeLojas;

  if(!grupoDeLojas) return res.json({});

  res.json(Resposta.sucesso(grupoDeLojas))

});

router.get('/empresas/me/:meio/pk',    (req: any, res ) => {
  let publicKey: string;

  if(req.params.meio ===  EnumMeioDePagamento.PagarmeHub){
    publicKey = req.empresa.integracaoGatewayPagamento ?  req.empresa.integracaoGatewayPagamento.publicKey : null;
  } else {
    let formaDePagamento =
      req.empresa.formasDePagamento.find((item: any) =>
        item.online && item.configMeioDePagamento.meioDePagamento === req.params.meio);

    if(formaDePagamento)
      publicKey =  formaDePagamento.configMeioDePagamento.publicKey

  }

  if(publicKey){
    res.json({publicKey: publicKey})
  } else {
    res.json({erro: 'Forma de pagamento não habilitada'})
  }
})

router.get('/empresas/me', async (req: any, res) => {
  let empresa: any = req.empresa;

  if(!empresa) return res.json({});

  empresa.exibirWizard =  !empresa.completouWizard();

  if(empresa.temPedidos())
    empresa.linkLoja = empresa.obtenhaLinkLoja(Ambiente._instance.producao)

  const mapeadorDeNotificacao = new MapeadorDeNotificacao();

  const notificacao: Notificacao = await mapeadorDeNotificacao.selecioneSync({
    tipoDeNotificacao: TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido
  });

  if(empresa.configImpressao && empresa.configImpressao.imprimirTXT)
    for(let impressora of empresa.configImpressao.impressoras) {
      if(empresa.configImpressao.modoHTML)
        impressora.modoHTML = true

      if(empresa.configImpressao.emitirBeep) {
        impressora.emitirBeep = true
        impressora.duracaoBeep = empresa.configImpressao.duracaoBeep ? empresa.configImpressao.duracaoBeep : 500
        impressora.quantidadeBeeps = empresa.configImpressao.quantidadeBeeps ?
          empresa.configImpressao.quantidadeBeeps : 1
      }


      impressora.cortarAutomatico = empresa.configImpressao.cortarAutomatico

      impressora.ocultarNumeroCliente = empresa.configImpressao.ocultarNumeroCliente
    }

    let empresaService = new EmpresaService()
    empresaService.lojaEstahFechada(empresa).then( async (resposta) => {
    const usuarioFechou = resposta.fechada;

    empresa.setHorariosFuncionamento(EnumServicoHorarioFuncionamento.Site);
    empresa.setFormasPagamentosDaLoja();

    if( usuarioFechou )
      empresa.setEmpresaFechadaPeloUsuario(resposta.mensagem)

    if(empresa.integracoesIfood.length )   CacheService.pingMerchant(empresa)

    empresa.enviarMensagemSaudacao = (notificacao && notificacao.ativada);
    empresa.darkPrincipal =  empresa.ehDarkPrincipal()

    if(empresa.integracaoPedidoFidelidade)
      (empresa.integracaoPedidoFidelidade as any).pontuando = empresa.integracaoPedidoFidelidade.estaPontuando();

    empresa.pizzaria = empresa.segmento &&  empresa.segmento.id === 1;

    empresaService.configureDisponibilidadeCategorias(empresa)

    res.json(empresa);
  });
});

router.get('/sse/assinantes', (req: any, res: any) => {
  let q = req.query.q;

  GerenciadorDeAssinantesSSE.Instancia().obtenhaTodosAssinantes().then( (chaves: any) => {
    if( q ) {
      q = q.toUpperCase();
      const assinantes = chaves.assinantes;
      chaves.assinantes = assinantes.filter( (elemento: any) => {
        return elemento.empresa.nome.toUpperCase().indexOf(q) !== -1;
      });
    }

    res.json({
      sucesso: true,
      data: chaves
    });
  });
});

router.get('/sse/assinantesrede', (req: any, res: any) => {
  const usuario = req.user;
  let adminRede = usuario.adminRede

  GerenciadorDeAssinantesSSE.Instancia().obtenhaTodosAssinantes().then( (chaves: any) => {
    if( adminRede ) {
      adminRede = adminRede.toUpperCase();
      const assinantes = chaves.assinantes;
      chaves.assinantes = assinantes.filter( (elemento: any) => {
        return elemento.empresa.nome.replaceAll(' ', '').toUpperCase().indexOf(adminRede) !== -1;
      });
    }

    res.json({
      sucesso: true,
      data: chaves
    });
  });
});

router.get('/mensagens/stream', (req, res) => {
  // SSE Setup

  let idUltimaMensagem = req.query.id;

  if( !idUltimaMensagem ) {
    return res.json({
      sucesso: false
    });
  }

  const id = Number(idUltimaMensagem);

  if( isNaN(id) ) {
    return res.json({
      sucesso: false
    });
  }

  req.socket.setTimeout(0);

  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'X-Accel-Buffering': 'no'
  });
  res.write('\n');

  GerenciadorDeAssinantesSSE.Instancia().sseDemo(req, res);
});

router.get('/mensagens/proxima', async(req: any, res: any) => {
  const idMensagem = req.query.id;

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    const objeto = {tipo: 'enviou-mensagens', id: idMensagem};

    publisher.publish(empresa.id, JSON.stringify(objeto));

    res.json({
      sucesso: true
    });
  });
});

router.get('/mensagens/enviei', async (req: any, res: any) => {
  const idMensagem = req.query.id;
  let empresa: Empresa = req.empresa;
  const idWhatsapp = req.query.idw;
  let status: any = req.query.s;

  console.log('enviou mensagem ' + idMensagem)
  const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

  if( empresa.dark ) {
    mapeadorDeMensagemEnviada.desativeMultiCliente();
  }

  mapeadorDeMensagemEnviada.selecioneSync({id: idMensagem}).then( async (mensagemEnviada: MensagemEnviada) => {
    if( !mensagemEnviada ) {
      return res.json({
        sucesso: false,
        mensagem: 'Mensagem não encontrada'
      });
    }

    mensagemEnviada.qtdeTentativas ++;

    if( status === 'NAO_TEM_WHATSAPP' ) {
      mensagemEnviada.status = StatusDeMensagem.NAO_TEM_WHATSAPP;
    } else if( status === 'IMPOSSIVEL_ENVIAR' ) {
      mensagemEnviada.status = StatusDeMensagem.IMPOSSIVEL_ENVIAR;
    } else if( status === 'ENVIADA' ) {
      mensagemEnviada.status = StatusDeMensagem.Enviada;
    } else if( status === 'CONTATO_BLOQUEADO' ) {
      mensagemEnviada.status = StatusDeMensagem.CONTATO_BLOQUEADO;
    } else {
      if( mensagemEnviada.qtdeTentativas >= 3 ) {
        mensagemEnviada.status = StatusDeMensagem.Falhou;
      }
    }

    if( idWhatsapp ) mensagemEnviada.idWhatsapp = idWhatsapp;

    mapeadorDeMensagemEnviada.atualizeSync(mensagemEnviada).then( async(atualizou: any) => {
      if( mensagemEnviada.campanha ) {
        const campanha = mensagemEnviada.campanha;

        const mapeadorDeCampanha = new MapeadorDeCampanha();
        /* mudar aqui */
        if( campanha.qtdeEnviadas + 5 >= campanha.qtdeMensagens && mensagemEnviada.tipoDeNotificacao !==
          TipoDeNotificacaoEnum.TesteCampanha ) {
          // Atualize a contagem de qtde_enviadas
          let campanhaDoBD = await mapeadorDeCampanha.atualizeTotalEnviadas(campanha);

          campanha.qtdeEnviadas = campanhaDoBD.qtdeEnviadas;

          if (campanha.qtdeEnviadas === campanha.qtdeMensagens) {
            campanha.status = EnumStatusCampanha.Enviada;
          }
        } else {
          campanha.qtdeEnviadas ++;
        }

        new MapeadorDeCampanha().atualizeStatus(campanha).then( () => {
          res.json({
            sucesso: true
          });
        });

        return;
      }

      res.json({
        sucesso: true
      });
    }).catch( (erro: Error) => {
      console.log('Houve um erro ao enviar')
      if(erro)
       console.log(erro.message)

      res.json(Resposta.erro('Falhou'));
    });
  });
});

/*
router.get('/mensagens', (req, res) => {
  console.log(req.query);

  const horario = req.query.h;
  const horarioModificacao = new Date(horario);
  const inicio = 0;
  let total = 10;
  let prioridade = req.query.p;

  if( !prioridade ) {
    prioridade = null;
  }

  if( prioridade === 'baixa' ) {
    total = 5;
  }

  console.log(horarioModificacao);

  if( isNaN(horarioModificacao.getTime()) ) {
    return res.json(Resposta.erro('Horário inválido: ' + horarioModificacao));
  }

  console.log('horario: ' + horarioModificacao);
  const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

  mapeadorDeMensagemEnviada.listeAsync({
    pendentes: true,
    horarioModificacao: horarioModificacao,
    inicio: inicio,
    total: total,
    prioridade: prioridade
  }).then( (mensagens) => {
    for( let i = 0; i < mensagens.length; i ++ ) {
      const mensagem = mensagens[i];

      mensagem.contato.sts = mensagem.contato.obtenhaDescStatus();
    }

    res.json({
      sucesso: true,
      data: mensagens
    });
  });
});
*/

router.post( '/cliente/enviePendentes/:numero', (req, res) => {
  let numero = req.params.numero;

  if(!numero) {
    return res.json(Resposta.erro("É necessário informar o número para o qual as mensagens pendentes de ativação " +
      "serão enviadas."));
  }

  const mapeador = new MapeadorDeContato();

  mapeador.selecioneSync({telefone: numero}).then( (contato: Contato) => {
    if ( !contato ) {
      const situacao = new SituacaoDeMensagem();
      situacao.sucesso = false;

      return res.json({
        situacao
      });
    }

    contato.status = EnumStatusContato.Ativo;
    contato.dataAtivacao = new Date();

    new MapeadorDeContato().atualizeStatus(contato).then(() => {
      res.setHeader("Content-Type", "application/json; charset=utf-8");
      res.json(Resposta.sucesso(contato));
    });
  });
});

router.get('/cliente/valide', (req, res) => {
  let codigo = req.query.codigo;
  let telefone = req.query.telefone;

  if(!codigo)
    return res.json(Resposta.erro("É necessário informar o número para o qual o cartão será enviado"));

  const mapeadorDeContato = new MapeadorDeContato();

  const mapeadorDeCodigoDeVerificacao = new MapeadorDeCodigoDeVerificacao();

  mapeadorDeCodigoDeVerificacao.selecioneSync({codigo: codigo, telefone: telefone}).then( (codigoVerificacao: CodigoVerificacao) => {
    if (!codigoVerificacao) {
      return res.json(Resposta.erro("Código inválido. Verifique se digitou o codigo correto."));
    }

    mapeadorDeContato.selecioneSync({telefone: telefone}).then((contato: Contato) => {
      if( contato ) {
        if (contato.status === EnumStatusContato.Ativo) {
          return res.json(Resposta.sucesso(contato));
        }

        contato.status = EnumStatusContato.Ativo;
        contato.dataAtivacao = new Date();

        new MapeadorDeContato().atualizeStatus(contato).then(() => {
          res.setHeader("Content-Type", "application/json; charset=utf-8");
          res.json(Resposta.sucesso(contato));
        });
      } else {
        res.json({
          sucesso: true,
          data: {
            id: null,
            codigo: 'NOVO_CONTATO'
          }
        });
      }
    });
  });
});

router.get('/cliente/enviarLink/:numero', (req, res) => {
  let numero = req.params.numero;

  if(!numero)
    return res.json(Resposta.erro("É necessário informar o número para o qual o cartão será enviado"));

  const mapeadorDeContato = new MapeadorDeContato();

  const codigo = randtoken.generator({
    chars: '0-9'
  }).generate(6);

  const codigoDeVerificacao = new CodigoVerificacao(numero, codigo);
  const novoEnviadorDeMensagens = Ambiente.Instance.novoEnviadorDeMensagens(Ambiente.Instance.contexto().empresa,
    true);
  const notificacaoService = new NotificacaoService(novoEnviadorDeMensagens);
  const empresa: Empresa = Ambiente.Instance.contexto().empresa;

  mapeadorDeContato.selecioneSync({telefone: numero}).then( (contato: Contato) => {
    if(!contato) {
      const respostaErro = new Resposta("Compareça ao estabelecimento para criar seu Cartão Fidelidade");

      respostaErro.erro = 'Não foi possível encontrar um contato associado ao número informado.';
      respostaErro.data = 'CONTRATO_NAO_ENCONTRADO';
      respostaErro.sucesso = false;

      const mensagem = codigoDeVerificacao.codigo + ' e o codigo para acessar seu cartao fidelidade da ' + empresa.dominio;

      new MapeadorDeCodigoDeVerificacao().insiraSync(codigoDeVerificacao).then( (atualizou: any) => {
        notificacaoService.envieSMSCodigo(mensagem, numero).then((resposta: Resposta<any>) => {
          if( resposta.data && resposta.data.jaEnviado ) {
            resposta.sucesso = false;

            return res.json(resposta);
          }
          return res.json(respostaErro);
        });
      });

      return;
    }

    contato.cartoes[0].codigoTemp = codigo;
    console.log("código gerado: " + codigo);

    new MapeadorDeCodigoDeVerificacao().insiraSync(codigoDeVerificacao).then( (atualizou: any) => {
      console.log('Criando enviador de mensagens do tipo ' + Ambiente.Instance.contexto().empresa.meioDeEnvio);

      console.log(novoEnviadorDeMensagens);

      const mensagem = codigoDeVerificacao.codigo + ' e o codigo para acessar seu cartao fidelidade da ' + empresa.dominio;

      notificacaoService.envieSMSCodigo(mensagem, numero).then((resposta: Resposta<any>) => {
        if (resposta && !resposta.sucesso) {
          return res.json(resposta);
        }

        return res.json(resposta)
      });
    });
  });
});

router.get('/cliente/confirmar/:idtoken', (req, res) => {

  let idEToken: any = req.params.idtoken;

  if (!idEToken) {
    return res.json(Resposta.erro('É necessário informar o token do contato a ser confirmado.'));
  }
  idEToken = idEToken.split(".");
  const id = idEToken[0];
  const token = idEToken[1];

  const mapeador = new MapeadorDeContato();

  mapeador.selecioneSync({id: id}).then( (contato: Contato) => {
    if (!contato)  return res.json(Resposta.erro('Contato inválido'));
    if (contato.token !== token) return res.json(Resposta.erro('Token inválido'));
    if (contato.status === EnumStatusContato.Ativo) { return res.json(Resposta.sucesso(contato)); }

    contato.status = EnumStatusContato.Ativo;
    contato.dataAtivacao = new Date();
    mapeador.atualizeStatus(contato).then( () => {
      res.setHeader("Content-Type", "application/json; charset=utf-8");
      res.json(Resposta.sucesso(contato));
    });
  });
});

function obtenhaPedidos(latitude: number, longitude: number, pagina: number) {
  return new Promise( (resolve, rejct) => {
    let link = 'https://marketplace.ifood.com.br/v1/merchants?latitude=' +
      latitude + '&longitude=' + longitude + '&zip_code=00000000&page=' + pagina + '&channel=IFOOD&size=100&' +
      'sort=&categories=&payment_types=&delivery_fee_from=0&delivery_fee_to=25&delivery_time_from=0&delivery_time_to=90';

    console.log(link);

    const options = {
      method: 'GET',
      uri: link,
      headers: {
        'Accept': '*/*',
        'Connection': 'keep-alive',
        'Accept-Charset': 'utf-8'
      }
    };

    // @ts-ignore
    request(options)
      .then( async (body) => {
        const resposta = JSON.parse(body);

        const clientes = resposta.merchants;

        if( clientes === null ) {
          console.log(resposta);
          resolve([]);
        }

        const nomes = [];

        for( let i = 0; i < clientes.length; i++ ) {
          const cliente = clientes[i];

          const objCliente = {
            nome: cliente.name,
            link: 'https://www.ifood.com.br/delivery/' + cliente.slug + "/" + cliente.id,
            superRestaurante: cliente.tags.indexOf('SUPER_RESTAURANT') !== -1,
            nota: cliente.userRating
          };

          nomes.push(objCliente);

          await obtenhaDadosLojaIfood(cliente.id, objCliente);
        }

        resolve(nomes);
      });
  });
}

async function obtenhaDadosLojaIfood(id: string, objCliente: any): Promise<void> {
  return new Promise( (resolve, reject) => {
    const url = 'https://marketplace.ifood.com.br/v1/merchants/' + id + '/extra';

    axios.get(url).then( (dados: any) => {
      const cnpj = dados.data.documents.CNPJ.value;
      const categoria = dados.data.mainCategory.friendlyName;
      const endereco = dados.data.address;

      objCliente.cnpj = cnpj;
      objCliente.categoria = categoria;
      objCliente.endereco = `${endereco.streetName} num. ${endereco.streetNumber} ${endereco.city} - ${endereco.state}`;
      objCliente.localizacao = endereco.latitude + "," + endereco.longitude;

      resolve();
    });
  });
}

router.get('/localizacao', async(req: any, res) => {
  const idEmpresa = (new VariaveisDeRequest()).obtenhaIdEmpresaLogada();
  const endereco = req.query.e;
  const setor = req.query.s;

  if( !endereco ) {
    return res.json(Resposta.erro('Parâmetros inválidos'));
  }

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( async (empresa: Empresa) => {
    new TomtomService().calculeCoordenadas(empresa, endereco).then( (resposta) => {
      res.json(resposta);
    });
  });
});

router.post('/localizacaoEndereco', async(req: any, res) => {
    const dados = req.body;
    const formaEscolhida = dados.forma;
    const zonaDeEntrega = dados.zona;

    let localizacao = '';

    if( dados.endereco.localizacao && dados.endereco.localizacao.lat) {
      localizacao = dados.endereco.localizacao.lat + "," + dados.endereco.localizacao.lon;
    }

    /*
    if( dados.endereco.localizacao ) {
      localizacao = dados.endereco.localizacao;
    }
     */

    const endereco = new Endereco(null, null, dados.endereco.cidade,
      dados.endereco.cep, dados.endereco.logradouro, dados.endereco.complemento, dados.endereco.bairro, dados.endereco.numero,
      dados.endereco.descricao, localizacao);

    let empresa: Empresa = req.empresa;

    const formaDeEntregaReceberEmCasa: FormaDeEntregaEmpresa = empresa.obtenhaFormaReceberEmCasa();

    const tomtomService = new TomtomService();

    let enderecoEncontrado: any = null;

    if (!endereco.localizacao) {
      enderecoEncontrado = await tomtomService.calculeCoordenadas(empresa, endereco);

      if (enderecoEncontrado)
        endereco.localizacao = enderecoEncontrado.localizacao.obtenhaString();
    } else {
      enderecoEncontrado = Object.assign(Endereco.novo(), endereco);
      enderecoEncontrado.localizacao = Localizacao.fromString(endereco.localizacao);
    }

    enderecoEncontrado.localizacao = enderecoEncontrado.localizacao.obtenhaString();

    return res.json(Resposta.sucesso(enderecoEncontrado));
});

router.post('/taxaDeEntrega', async(req: any, res) => {
  const dados = req.body;
  const formaEscolhida = dados.forma;
  const zonaDeEntrega = dados.zona;

  let localizacao =  '';

  if(dados.endereco.localizacao){
    if(  dados.endereco.localizacao.lat)
      localizacao = dados.endereco.localizacao.lat + "," + dados.endereco.localizacao.lon;
    else  if(dados.endereco.localizacao.indexOf('undefined') === -1) // nao pegar endereços salvos errados
      localizacao = dados.endereco.localizacao;
  }

  const endereco = new Endereco(null, null, dados.endereco.cidade,
    dados.endereco.cep, dados.endereco.logradouro, dados.endereco.complemento, dados.endereco.bairro, dados.endereco.numero,
    dados.endereco.descricao, localizacao);

  let empresa: Empresa = req.empresa;

  const formaDeEntregaReceberEmCasa: FormaDeEntregaEmpresa = empresa.obtenhaFormaReceberEmCasa();

  if(formaDeEntregaReceberEmCasa.cepObrigatorio && !endereco.cep)
       return res.json(Resposta.erro('Cep não informado! verifique o endereço de entrega'))

  //se não tem kml, calcular pegando no site do china inbox
  if( !formaDeEntregaReceberEmCasa.possuiKML() && empresa.fazParteDaRedeChinaInBox() ) {
    console.log(`${empresa.id} - ${empresa.nome} [China] Calculando taxa de entrega usando site do china inbox`);

    let resposta: any = { taxaDeEntrega: 0.0, localizacao:   ''};

    const chinaInBoxService = new ChinaInBoxService();

    const respLoja = await chinaInBoxService.localizeLojaTOR(empresa, endereco.cep, endereco.numero);

    if( respLoja.sucesso ) {
      const loja = respLoja.data.loja;

      if( !loja ) {
        return res.json(Resposta.erro(FormaDeEntregaEmpresa.MSG_NAO_ENTREGA))
      }

      if(!respLoja.data.lojaCerta) {
        return res.json(Resposta.erro(FormaDeEntregaEmpresa.MSG_NAO_ENTREGA))
      }

      let chave = endereco.obtenhaChaveHash(empresa);
      let hash = crypto.createHash('md5').update(chave).digest('hex');
      resposta.taxaDeEntrega = loja.taxaDeEntrega;

      CacheService.insiraJson(hash, resposta.taxaDeEntrega, 60 * 60 * 24 ) //24hs
      resposta.hash = hash;
    }

    return res.json(Resposta.sucesso(resposta));
  }

  let forma: any =  _.find(empresa.formasDeEntrega, (formaCorrente: any) =>
     formaEscolhida === (formaCorrente.nome)
  );

  if( !forma )
    return res.json(Resposta.erro('Parametros inválidos'));

  const formaDeEntrega = FormaDeEntregaEmpresa.novaDaCache(forma)

  formaDeEntrega.calcule(empresa, endereco, dados.valor, zonaDeEntrega).then( async(taxaDeEntregaCalculada: TaxaDeEntregaCalculada) => {
    if(taxaDeEntregaCalculada.sucesso) {
      let chave = endereco.obtenhaChaveHash(empresa);
      let hash = crypto.createHash('md5').update(chave).digest('hex');

      CacheService.insiraJson(hash, taxaDeEntregaCalculada.valor, 60 * 60 * 24 ) //24hs

      taxaDeEntregaCalculada.hash = hash;
      console.log('#chave hash taxa entrega:')
      console.log(String(`${chave}:  ${hash}`))
    }

    const mapeador = new MapeadorDeTaxaDeEntregaCalculada();

    await mapeador.insiraGraph(taxaDeEntregaCalculada);

    res.json(taxaDeEntregaCalculada.obtenhaResposta());
  }).catch( (err) => {});
});

router.get('/leads', async(req, res) => {
  const listaDeLeads = [];

  let fs  = require('fs');

  const anapolis = {
    lat: -16.328500,
    lon: -48.953400
  };
  const goiania = {
    lat: -16.6528326,
    lon: -49.2418583
  };
  const campinhasSp = {
    lat: -22.932900,
    lon: -47.073800
  };
  const brasilia = {
    lat: -15.826700,
    lon: -47.921800
  };
  const recife = {
    lat: -8.057799994192788,
    lon: -34.883093121325345
  }
  const beloHorizonte = {
    lat: -19.8157,
    lon: -43.9542
  }
  const contagem = {
    lat: -19.9386,
    lon: -44.0529
  }
  const saoJose = {
    lat: -23.1791,
    lon: -45.8872
  }
  const salvador = {
    lat: -12.9704,
    lon: -38.5124
  };
  const natal = {
    lat: -5.808891955010713,
    lon: -35.225025487689095
  };
  const maceio = {
    lat: -9.665967104351534,
    lon: -35.735100144437816
  };
  const joaoPessoa = {
    lat: -7.116339787268697,
    lon: -34.86391488221047
  };
  const rioverde = {
    lat: -17.792981397612024,
    lon: -50.928073852123234
  };
  const aracaju = {
    lat: -10.9095,
    lon: -37.0748
  }

  const nomeArquivo = 'contatos_ifood_natal.csv';

  fs.writeFileSync(nomeArquivo, '');
  fs.appendFileSync(nomeArquivo, 'Categoria;Nome;Super Restaurante;Nota;Link;CNPJ;Endereço;Localização\n');
  for( let i = 0; i < 20; i++ ) {
    const nomes: any = await obtenhaPedidos(natal.lat, natal.lon, i);

    console.log('nomes: ' + nomes.length);

    if( nomes.length === 0 ) {
      break;
    }

    for( let j = 0; j < nomes.length; j++ ) {
      const lead = nomes[j];

      const linha = lead.categoria + '; ' + lead.nome + '; ' + (lead.superRestaurante ? 'Sim' : 'Não') + '; ' +
        (lead.nota.toFixed(2).replace('.', ',')) +
        '; ' + lead.link + '; ' + ('00000000000000' + lead.cnpj).slice(-14) + '; ' +
        lead.endereco + ';' + lead.localizacao + '\n';
      fs.appendFileSync(nomeArquivo, linha);
    }
  }

  res.json({
    sucesso: true
  });
});

router.get('/empresa/:idEmpresa/produtos/:id', async (req, res) => {
  const idProduto: any = req.params.id;
  new MapeadorDeEmpresa().selecioneSync({id: req.params.idEmpresa}).then((empresa: Empresa) => {
    new MapeadorDeProduto(empresa.catalogo).selecioneSync({id: idProduto}).then( (produto) => {
      res.json({
        sucesso: true,
        data: produto
      })
    });
  })

});

router.get('/empresa/:idEmpresa/produtos', async (req: any, res: any) => {
  const idEmpresa = req.params.idEmpresa;
  const temEstoque = req.query.temEstoque === 'true';
  const tipoCardapio = req.query.tipoCardapio;

  let dados: any = {
    idEmpresa: idEmpresa,
    temEstoque: temEstoque,
    cardapioDelivery: tipoCardapio === 'DELIVERY',
    cardapioMesa: tipoCardapio === 'MESA'
  };

   new MapeadorDeEmpresa().selecioneSync({id: idEmpresa}).then((empresa: Empresa) => {
     new MapeadorDeProduto(empresa.catalogo).listeAsync(dados).then( (produtos: any) => {
       res.json(Resposta.sucesso(produtos));
     });

   })

});

router.get('/produtos/vitrines', async (req: any, res) => {

  let vitrines = await new MapeadorDeVitrine().listeAsync({ disponivel: true});

  res.json(Resposta.sucesso(vitrines));
});

router.get('/grupo/produtos/venda', async (req: any, res) => {
  const mapeadorDeGrupoDeLojas = new MapeadorDeGrupoDeLojas();

  const grupo: GrupoDeLojas = req.grupoDeLojas;

  for( let empresaDoGrupo of grupo.empresas ) {
    let contexto =  require('domain').active.contexto;
    contexto.idEmpresa = empresaDoGrupo.empresa.id;

    console.log('buscando: ' + empresaDoGrupo.empresa.id + ' ' + empresaDoGrupo.empresa.nome);

    const produtos = await new ProdutoService().obtenhaProdutosAVenda(empresaDoGrupo.empresa,
      'DELIVERY', true, empresaDoGrupo.empresa.nomeCategoriaDestaques, 100,
      0, '', 0);

    empresaDoGrupo.produtos = produtos;

    empresaDoGrupo.produtos.forEach((produto: any) => {
       produto.idEmpresa = empresaDoGrupo.id;
    })
  }

  res.json(Resposta.sucesso(grupo.empresas));
});


router.get('/produtos/venda/:cardapio/async', async (req: any, res: any) => {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  console.log('nome empresa:' + req.header('nomeempresa'))

  let tipoCardapio = req.params.cardapio.toUpperCase();

  const nomeCategoria = req.query.cat
  const idCategoria = req.query.idcat ? Number(req.query.idcat) : null
  const vitrines = req.query.vit
  const limite = req.query.limite
  const indice = req.query.indice
  const termo = req.query.termo ?   req.query.termo.trim() : null;
  const contatoLogado = req.session.contatoLogado;
  const empresa: any = req.empresa;

  let temBusca = termo || idCategoria || nomeCategoria || vitrines;

  console.log('buscando produtos async da: ' + req.empresa.nome);
  //let inicio = moment();
  new ProdutoService().obtenhaProdutoseCategoriasAVendaNaLoja(empresa, tipoCardapio, nomeCategoria, limite,
    indice, termo, idCategoria, vitrines).then( async (resposta: any) => {


    CategoriaProdutosUtils.definaCategoriasFixas(resposta, empresa);

    // Envia as categorias como evento "categorias"
    res.write(`event: categorias\ndata: ${JSON.stringify({ categorias: resposta.categorias })}\n\n`);

    resposta.produtos.forEach((produto: any) => {
      if(produto.template && produto.template.ocultarProdutos)
        produto.categoria.ocultar = true;
    })

    // Envia os produtos como evento "produtos"
    res.write(`event: produtos\ndata: ${JSON.stringify({ produtos: resposta.produtos })}\n\n`);
    res.end();
  });
});

router.get('/produtos/venda/:cardapio', async (req: any, res) => {
  let tipoCardapio = req.params.cardapio.toUpperCase();

  const nomeCategoria = req.query.cat
  const idCategoria = req.query.idcat ? Number(req.query.idcat) : null
  const vitrines = req.query.vit
  const limite = req.query.limite
  const indice = req.query.indice
  const termo = req.query.termo ?   req.query.termo.trim() : null;
  const contatoLogado = req.session.contatoLogado;
  const empresa: Empresa = req.empresa;

  let temBusca = termo || idCategoria || nomeCategoria || vitrines;

  console.log('buscando produtos da: ' + req.empresa.nome);
  //let inicio = moment();
  new ProdutoService().obtenhaProdutoseCategoriasAVendaNaLoja(req.empresa, tipoCardapio, nomeCategoria, limite,
    indice, termo, idCategoria, vitrines).then( async (resposta: any) => {

    if(contatoLogado && contatoLogado.id && !temBusca) {
      resposta.ultimosPratos =  [];
      //let ultimosPedidos = await new MapeadorDePedido().listeUltimosPedidos(contatoLogado);
      /*
      ultimosPedidos.forEach((pedido: Pedido) => {
        pedido.itens.forEach((itemPedido: ItemPedido) => {
           let adicionarPrato = !resposta.ultimosPratos.find((item: any) => item.produto.id === itemPedido.produto.id)

           if(adicionarPrato){
             let produtoCompleto = resposta.produtos.find((produto: any) => produto.id === itemPedido.produto.id);

             if(produtoCompleto){
               delete itemPedido.pedido;
               itemPedido.produto = produtoCompleto;
               resposta.ultimosPratos.push(new DTOItemPedido(itemPedido))
             }
           }
        })
      })
       */
    }

    resposta.produtos.forEach((produto: any) => {
      if(produto.template && produto.template.ocultarProdutos)
        produto.categoria.ocultar = true;

       produto.brinde = produto.brindeFidelidade();

    })

    if(tipoCardapio !== 'MESA'){
      let brindes: Array<Produto> = resposta.produtos.filter((item: any) => item.brinde);

      if(brindes.length){
        resposta.produtos = resposta.produtos.filter((item: any) => !item.brinde);
        if(empresa.resgatarBrindes())
          resposta.brindes = brindes;
      }
    }


    // const key = 'produtos_empresa::' + req.empresa.id;
    // if( (empresa.id === 604 || empresa.id === 663 || empresa.id === 637 || empresa.id === 784) ) {
    //  CacheService.insiraNoMapJson(key, req.url, resposta);
    //}
    //console.log(String(`- tempo total resposta produtos a venda: ${moment().diff(inicio, 's')}s`))
    res.json(Resposta.sucesso(resposta));

  });
})

router.get('/categorias/megamenu', async (req: any, res) => {
  let empresa = req.empresa
  let todasCategorias = await new MapeadorDeCategoria(empresa.catalogo).listeAsync({orderBy: true});
  let categoriasPai: any =  todasCategorias.filter((categoria: any) => categoria.nivel === 1);

  for(let i = 0; i < categoriasPai.length; i++){
    let categoriaPai = categoriasPai[i];

    categoriaPai.subcategorias =
      todasCategorias.filter((categoria: any) => categoria.categoriaPai && categoria.categoriaPai.id === categoriaPai.id);

    for(let j = 0; j < categoriaPai.subcategorias.length; j++){
      let subCategoria = categoriaPai.subcategorias[j];

      subCategoria.subcategorias =
        todasCategorias.filter((categoria: any) => categoria.categoriaPai && categoria.categoriaPai.id === subCategoria.id);
    }

   // categoriaPai.subcategorias = categoriaPai.subcategorias.filter( ((categoria: any) => categoria.subcategorias.length  > 1))
  }

  categoriasPai = categoriasPai.filter( ((categoria: any) => categoria.subcategorias.length  > 1))

  res.json(Resposta.sucesso(categoriasPai));
})

router.get('/produtos/autocomplete', async (req: any, res) => {
  let produtos: any = []
  let dados: any = {temEstoque: true};
  dados.inicio = Number(req.query.i || 0)
  dados.total = Number(req.query.t || 50)

  if(req.query.q)
    dados.termo  = String(`%${req.query.q}%`);

  let buscarAdicionaisObrigatorios = req.query.bo === '1'
  if(req.query.ez === '1')
    dados.esconderZerados = true

  const empresa: Empresa = req.empresa

  new MapeadorDeProduto(empresa.catalogo).listeAsync(dados).then( async (produtosBusca: any[]) => {

    for (const produto of produtosBusca) {   await  produto.processeParaLoja(empresa.getCashback()); }

    produtosBusca = produtosBusca.filter((produto: Produto) =>  produto.adicionaisObrigatoriosEstaoDisponiveis());

    produtos = produtosBusca.map(  produto => (
        { id: produto.id,
          nome: produto.nome, valor: produto.valorUnitario,
          categoria: produto.categoria ? produto.categoria.nome : '',
          camposAdicionais: buscarAdicionaisObrigatorios && produto.camposAdicionais ? produto.camposAdicionais.filter((adicional: any) =>
                                            adicional.obrigatorio).map((adicionalFiltrado: any) => { return {
                                                                                      id: adicionalFiltrado.id,
                                                                                      nome: adicionalFiltrado.nome,
      }} ) : null
    }))

    produtos.forEach((produto: any) => {
      produto.descricao = String(`${produto.id} - ${produto.nome}`)
      if(produto.categoria)
        produto.descricao = String(`${produto.descricao} em  "${produto.categoria}"`)
    })

    res.json(Resposta.sucesso(produtos))
  });

})

let logoChina: any = 'https://www.chinainbox.com.br/ccstore/v1/images/?source=/file/general/logo-lg-china.png&height=89&width=130';
async function  busqueLojas(grupoDeLojas: GrupoDeLojas, req: any, total: number = 12): Promise<Array<Empresa>> {
  let q = req.query.q;
  let query: any = {idGrupo: grupoDeLojas.id, orderNome: true, inicio: 0, total: total};

  if(req.query.i)
    query.inicio = Number(req.query.i);

  if(q)
    query.q = String(`%${q}%`)

  if( req.body.endereco ) {
    query.estado = req.body.endereco.cidade.estado;
  }

 // console.log(query)

  let lojas = await new MapeadorDeEmpresa().listeEmpresasRede(query)

  //console.log('Total lojas: ' + lojas.length)

  lojas.forEach((loja: any) => {
    loja.setHorariosFuncionamento(EnumServicoHorarioFuncionamento.Site);
    if(loja.estaAberta) {
      loja.formasDeEntrega.forEach( (forma: any) => {
        if(forma.tempoMinimo && forma.tempoMaximo)
          loja.descricaoTempoEntrega =  String(`${forma.tempoMinimo} ~ ${forma.tempoMaximo} min`);
      });
    }
    loja.logo = `https://${loja.dominio}.meucardapio.ai/images/empresa/` + loja.logo;
  });

  return lojas;
}

router.get('/franquia/lojas', async (req: any, res: any) => {
  const g: string = req.query.g;

  let dominio = req.get('host');
  let grupoDeLojas: GrupoDeLojas = null;

  if( g ) {
    grupoDeLojas = await GrupoDeLojasService.Instancia().obtenhaPorId(+g);
  } else {
    grupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);
  }

  let lojas = await busqueLojas(grupoDeLojas, req);

  res.json(Resposta.sucesso(lojas));
})

router.get('/obtenhaGrupoDaEmpresa', async (req: any, res) => {
  const empresa: Empresa = req.empresa;

  let gruposDeLojas: any = await new MapeadorDeGrupoDeLojas().selecioneSync({
    idEmpresa: empresa.id
  });

  res.json(Resposta.sucesso(gruposDeLojas));
});


router.get('/franquia/', async (req, res) => {
  let dominio = req.get('host');
  let grupoDeLojas: GrupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);

  let franquia: any = {
      rede: 'chinainbox',
      nome: "China In Box",
      logoTopo: '/images/franquia/cib/logo-china-topo.png',
      bannerPromo: 'https://promokit.meucardapio.ai/images/empresa/c830e640-d5ef-11eb-a73d-9d87a6b98c4a.png'
  };


  franquia.lojas = await busqueLojas(grupoDeLojas, req);

  res.setHeader("Content-Type", "application/json; charset=utf-8");

  res.json(Resposta.sucesso(franquia));
});

router.post('/encontreLojaGps/:grupoDeLojas', async(req: any, res: any) => {
  let dominio = req.get('host');
  let grupoDeLojas: GrupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);
  const posicao = req.body.p;

  if( !posicao ) {
    return res.json(Resposta.erro('Parametros invalidos'));
  }

  const localizacao = new Localizacao(posicao.lat, posicao.lng);

  const endereco: any = await new TomtomService().geocode(null, posicao.lat, posicao.lng);

  req.body.endereco = endereco;

  const lojas = await busqueLojas(grupoDeLojas, req, 1000);

  let dadosLoja: any = null;

  await funcaoEncontrarLoja(grupoDeLojas, lojas, localizacao, res);
});

async function funcaoEncontrarLoja(grupoDeLojas: GrupoDeLojas, lojas: Array<Empresa>, localizacao: any, res: any) {
  if( grupoDeLojas.encontrarLojaMaisProxima ) {
    const lojaQueEntrega = await grupoDeLojas.lojaMaisProximaQueEntrega(lojas, localizacao);

    if( !lojaQueEntrega || !lojaQueEntrega.empresa) {
      res.json(Resposta.sucesso({
        encontrou: false,
        msg: "Ainda não entregamos na sua região, mas você pode retirar seu pedido."
      }));

      return;
    }

    await respondaLojaQueEntrega(grupoDeLojas, lojaQueEntrega.empresa, localizacao, res);
    return;
  }

  for( const empresa of lojas ) {
    const formaReceberEmCasa = empresa.obtenhaFormaReceberEmCasa();

    if( !formaReceberEmCasa ) {
      continue;
    }

    const resposta: RespostaFazEntregaKml = await formaReceberEmCasa.fazEntregaGps(empresa, localizacao);

    console.log('buscou empresa tomtom: ' + empresa.nome)
    console.log(resposta)
    if (resposta.sucesso && resposta.fazEntrega) {
      await respondaLojaQueEntrega(grupoDeLojas, empresa, localizacao, res);
      return;
    }
  }

  res.json(Resposta.sucesso({
    encontrou: false,
    msg: "Ainda não entregamos na sua região, mas você pode retirar seu pedido."
  }));
}

router.post('/encontreLoja/:grupoDeLojas', async(req: any, res) => {
  const dados = req.body;
  let dominio = req.get('host');
  let grupoDeLojas: GrupoDeLojas = await GrupoDeLojasService.Instancia().obtenha(dominio);

  console.log('grupo loja: '  + grupoDeLojas)
  const empresaLogada: Empresa = req.empresa;

  if( !grupoDeLojas ) {
    grupoDeLojas = await new MapeadorDeGrupoDeLojas().selecioneSync({idEmpresa: empresaLogada.id});
  }

  const endereco = new Endereco(null, null, dados.endereco.cidade,
    dados.endereco.cep, dados.endereco.logradouro, dados.endereco.complemento, dados.endereco.bairro,
    dados.endereco.numero, dados.endereco.descricao, null);

  req.body.endereco = endereco;

  let lojas: Array<Empresa> = [];
  if( grupoDeLojas ) {
    lojas = await busqueLojas(grupoDeLojas, req, 1000);
  } else if( !empresaLogada.idRede ) {
    lojas = await new MapeadorDeEmpresa().listeEmpresasRede({redes: [empresaLogada.idRede]});
  } else {
    lojas = [empresaLogada];

  }

  req.query.g = '';

  const tomtomService = new TomtomService();

  const enderecoEncontrado: any = await tomtomService.calculeCoordenadas(null, endereco);

  await funcaoEncontrarLoja(grupoDeLojas, lojas, enderecoEncontrado.localizacao, res);
});

async function respondaLojaQueEntrega(grupoDeLojas: GrupoDeLojas, empresa: Empresa, localizacao: any, res: any) {
  let linkLoja = empresa.obtenhaLinkLoja(true);
  if( grupoDeLojas.paginaDirecionarPara === EnumPaginaGrupoDirecionar.WHATSAPP ) {
    linkLoja = 'https://api.whatsapp.com/send/?phone=55' + empresa.numeroWhatsapp.whatsapp + '&app_absent=0&text=' +
      encodeURIComponent(grupoDeLojas.msgConversaWhatsapp);
  }

  const mapeadorDeGrupoDeLojas = new MapeadorDeGrupoDeLojas();

  const grupos = await mapeadorDeGrupoDeLojas.listeAsync({idEmpresa: empresa.id});

  const grupoMultiMarca = encontreGrupoMultiMarca(grupos);

  if( grupoMultiMarca ) {
    linkLoja = 'https://' + grupoMultiMarca.hostname;
  }

  let dadosLoja: any = null;

  dadosLoja = {
    encontrou: true,
    id: empresa.id,
    nome: empresa.nome,
    link: linkLoja,
    localizacao: localizacao
  };

  res.json(Resposta.sucesso(dadosLoja));
}

function encontreGrupoMultiMarca(grupos: Array<GrupoDeLojas>) {
  for(let grupo of grupos ) {
    if( grupo.telaMultiLoja ) {
      return grupo;
    }
  }

  return null;
}

router.get('/testeasdf/teste', (req: any, res: any) => {
  const dados = {
    valor: 23
  };

  //const hash = crypto.createHash('md5').update(JSON.stringify(dados)).digest('base64');

  res.json({
    sucesso: true
  });
});

router.get('/empresa/googlemaps/key', (req: any, res: any) => {
  res.json( req.empresa ? req.empresa.googleMapsKey : null);
})

router.get('/empresa/', (req: any, res: any) => {
  const idEmpresa = (new VariaveisDeRequest()).obtenhaIdEmpresaLogada();
  const idOperador = req.query.op;

  const mapeadorEmpresa = new MapeadorDeEmpresa();
  const sessao = req.session;

  if( req.grupoDeLojas && !req.empresa ) {
    const empresaDoGrupo = req.grupoDeLojas.empresas[0];
    let empresa = empresaDoGrupo.empresa;

    mapeadorEmpresa.selecioneCachePoId(empresa.id).then( async (objEmpresa: Empresa) => {
      if (!objEmpresa)
        return res.json(Resposta.erro("Não foi possível identificar a empresa do grupo. Verifique a url e tente novamente."));

      if( sessao && sessao.tema ) {
        objEmpresa.tema = sessao.tema;
      }

      objEmpresa.setHorariosFuncionamento(EnumServicoHorarioFuncionamento.Site);
      objEmpresa.removaFormasNaoMapeadas(req.grupoDeLojas)
      objEmpresa.retireZonasDesativadas();
      delete objEmpresa.googleMapsKey;
      let tagsProdutos: any =     await new MepeadorDeTagProduto().listeDaCache(  TagProdutoTipoEnum.Alimentar)
      res.json(Resposta.sucesso({empresa: objEmpresa, tagsProdutos: tagsProdutos}));
    });

    return;
  }

  if (!idEmpresa) {
    return res.json(Resposta.erro("Não foi possível identificar a empresa logada. Verifique a url e tente novamente."));
  }

  mapeadorEmpresa.selecioneCachePoId(idEmpresa).then( async (empresa: Empresa) => {
    if( idOperador ) {
      const mapeadorDeUsuario = new MapeadorDeUsuario();

      let usuario = await mapeadorDeUsuario.selecioneSync({ id: idOperador});

      if( usuario && usuario.numeroWhatsapp ) {
        empresa.numeroWhatsapp = usuario.numeroWhatsapp;
      }
    }

    if( sessao && sessao.tema ) {
      empresa.tema = sessao.tema;
    }

    if(empresa.integracaoFidelidade)
      empresa.integracaoFidelidade.setLogoLinkExterno(empresa)

    if(empresa.integracaoOpenDeliveryComercianteAtiva())
      empresa.formasDePagamento = empresa.formasDePagamento.filter((iten: any) => iten.opendeliveryMethod)

   // if(empresa.integracaoPDVParceiroAtiva())
      //empresa.formasDePagamento = empresa.formasDePagamento.filter((iten: any) => iten.referenciaExterna != null ||  iten.online)

    const mapeadorDeProduto = new MapeadorDeProduto(empresa.catalogo);
    mapeadorDeProduto.listeAsync({ exibirSite: true }).then(  (produtos: any) => {
      if ( produtos ) { empresa.destaques = produtos; }

    (new MapeadorDeFoto()).listeAsync({}).then( async (fotos: Array<Foto>) => {
      if ( fotos ) { empresa.ambiente = fotos; }

      if(empresa.integracaoDelivery) {
        empresa.integracaoDelivery = FactoryIntegracaoDelivery.crie(empresa.integracaoDelivery)
        delete empresa.integracaoDelivery.formasDePagamento;
        delete empresa.integracaoDelivery.empresa;
      }

      if(empresa.integracaoPedidoFidelidade)
        (empresa.integracaoPedidoFidelidade as any).pontuando = empresa.integracaoPedidoFidelidade.estaPontuando();

      new EmpresaService().lojaEstahFechada(empresa).then( async (resposta) => {
        const usuarioFechou = resposta.fechada;

        let servico = req.query.pre ? EnumServicoHorarioFuncionamento.Presencial : EnumServicoHorarioFuncionamento.Site;

        empresa.setHorariosFuncionamento(servico);
        empresa.setFormasPagamentosDaLoja();
        empresa.retireZonasDesativadas();

        //todo: tratar tela de qual serviço pegar para gerar horarios agendamento
        empresa.horariosFuncionamento = empresa.horariosFuncionamento.filter(item => item.servico === servico);

        if(empresa.integracaoGatewayPagamento) delete empresa.integracaoGatewayPagamento.privateKey;

        if( usuarioFechou )
          empresa.setEmpresaFechadaPeloUsuario( resposta.mensagem);

        (empresa as any).layoutEcommerce = empresa.lojaEcommerce();
         empresa.removaFormasNaoMapeadas(req.grupoDeLojas)

        if(!empresa.tema){
          if(empresa.ehUmaCib())
            empresa.tema =  EnumTemas.ChinaInBox
        }

        (empresa as any).exibirBrindes = empresa.resgatarBrindes();
        (empresa as any).exibirFidelidade = ( empresa.resgatarBrindes() || empresa.acumulaCashback() )
          && !empresa.ocultarPontosFidelidade();
        (empresa as any).vendeOnline = empresa.vendeOnline();

        delete empresa.googleMapsKey;
        let tagsProdutos: any =     await new MepeadorDeTagProduto().listeDaCache(  TagProdutoTipoEnum.Alimentar)

        res.setHeader("Content-Type", "application/json; charset=utf-8");
          res.json(Resposta.sucesso({empresa: empresa, tagsProdutos: tagsProdutos}));
        });
      });
    });
  });
});


router.get('/empresa/instagram', function(req, res, next) {
  let contexto = require('domain').active.contexto;

  if(!contexto.empresa)
    return res.json(Resposta.erro("Não foi possível localizar a empresa do contexto."));

  let empresa = contexto.empresa;

  if(!empresa.instagram)
    return res.json(Resposta.sucesso({posts: [] }))

   const options = {
      method: 'GET',
      uri: "https://www.instagram.com/" + empresa.instagram + "/?__a=1" ,
      encoding: 'utf-8',
    };

    // @ts-ignore
    request(options)
      .then( (body) => {
        const resposta = JSON.parse(body);

        console.log(resposta);

        let  posts: any[] = [];

        try{
          let dados = JSON.parse(body),
            usuario = dados.graphql.user;

          for (let post of usuario['edge_owner_to_timeline_media']['edges'])
            posts.push( {
              link: 'https://instagram.com/p/' + post.node.shortcode,
              imagem: post.node.thumbnail_src
            });

          posts = posts.slice(0, 4);
        } catch (e){
          console.log(e)
        }

        return res.json(Resposta.sucesso({posts: posts}))



      })
      .catch(function (err) {
        return res.json(Resposta.erro("Houve um erro ao tentar carregar as mensagens: " + err));
      });
});

router.get('/brindes', (req, res) => {
  const mapeadorBrinde = new MapeadorDeBrinde();


  mapeadorBrinde.listeAsync({}).then( (brindes: any) => {
    res.json({
      sucesso: true,
      brindes: brindes
    });
  })
});


router.get('/testHorarioFuncionamento', (req: any, res: any) => {
  let empresa = req.empresa
  let horariosFuncionamento: any[] = empresa.horariosFuncionamento

 // let frase = '_Horário de funcionamento_:\n xx ás xxx / xx ás xxx ( Segunda á domingo )'

  horariosFuncionamento.sort((horario1: any, horario2: any) => {
    let diaDaSemana1 = horario1.diaDaSemana
    let diaDaSemana2 = horario2.diaDaSemana

    if(diaDaSemana1 === 0) diaDaSemana1 = 7
    if(diaDaSemana2 === 0) diaDaSemana2 = 7

    if(diaDaSemana1 !== diaDaSemana2)
      return diaDaSemana1 - diaDaSemana2

    let horaAbertura1 = Number.parseInt(horario1.horarioAbertura.substring(0, 2), 10)
    let horaAbertura2 = Number.parseInt(horario2.horarioAbertura.substring(0, 2), 10)

    return horaAbertura1 - horaAbertura2
  })

  let horariosDoDia: any = {}
  let diaAtual = 0
  let textoAtual = []
  let menorDia = 0
  for(let horarioFuncionamento of horariosFuncionamento) {

    if(horarioFuncionamento.diaDaSemana !== diaAtual) {
      if(textoAtual.length > 0) horariosDoDia[diaAtual] = textoAtual.join(" / ")

      textoAtual = []


      diaAtual = horarioFuncionamento.diaDaSemana

      if(horarioFuncionamento.funciona && menorDia === 0)
        menorDia = diaAtual

    }

    if(!horarioFuncionamento.funciona)
      continue;

    let horarioAbertura = horarioFuncionamento.horarioAbertura.substring(0, 5)
    let horarioFechamento = horarioFuncionamento.horarioFechamento.substring(0, 5)



    let horarioDoTurno = horarioAbertura + " às " + horarioFechamento

    textoAtual.push(horarioDoTurno)
  }

  if(textoAtual.length > 0) horariosDoDia[7] = textoAtual.join(" / ")

  let frase: string = horariosDoDia[menorDia]
  let rangeAtual: string = frase
  let menorDiaRange: any = menorDia
  let maiorDiaRange: any = menorDia

  for(let i = menorDia + 1; i <= 7; i++) {
    if(!horariosDoDia[i] || horariosDoDia[i] !== rangeAtual) {
      if(rangeAtual) {
        frase += " (" + DiaDaSemanaEnumLabel.get(menorDiaRange)

        if(maiorDiaRange > menorDiaRange)
          frase += " a " + DiaDaSemanaEnumLabel.get(maiorDiaRange)

        frase += ")" + "\n"
      }


      menorDiaRange = i
      rangeAtual = horariosDoDia[i]
      if(rangeAtual)
        frase += rangeAtual
    }
    if(horariosDoDia[i])
      maiorDiaRange = i
  }

  if(horariosDoDia[menorDiaRange]) {
    frase +=  " (" + DiaDaSemanaEnumLabel.get(menorDiaRange)

    if(maiorDiaRange > menorDiaRange)
      frase += " a " + DiaDaSemanaEnumLabel.get(maiorDiaRange)

    frase += ")" + "\n"
  }



  frase = "*Horario de funcionamento:*\n" + frase


  res.json(Resposta.sucesso(frase))
})

router.get('/testeSMSTWW', (req, res) => {
  const urlWebService = 'https://webservices2.twwwireless.com.br/reluzcap/wsreluzcap.asmx?wsdl';

  const numero = randtoken.generate(16);

  const args = {
    NumUsu:  'FIBONACCI',
    Senha: 'Fibo@129',
    SeuNum: numero,
    Celular: '62982301144',
    Mensagem: 'Ola Marcio'
  };

  let cliente: any = null;
  // tslint:disable-next-line:no-shadowed-variable
  soap.createClientAsync(urlWebService).then( (client: any) => {
    console.log(client.EnviaSMSAsync);
    cliente = client;

    const t = client.EnviaSMSAsync(args);

    return t;
  }).then((result: any) => {
      console.log(result);

      const dados = {
        NumUsu:  'FIBONACCI',
        Senha: 'Fibo@129',
        SeuNum: numero
      };

      setTimeout( () => {
        cliente.StatusSMSAsync(dados).then((resposta: any) => {
          res.json({
            sucesso: true,
            numero: numero
          });
        });
      }, 5000);
    }).catch( (erro: any) => {
      const s = cliente.lastRequest;
    });
});


router.get('/cliente/cadastro/:cpf', async (req: any, res) => {
  const cpf = req.params.cpf;

  if(cpf.trim().toString().length < 11)
    return res.json( Resposta.sucesso({}))

  let contato: Contato = await new MapeadorDeContato().selecioneSync({ cpf: cpf});
  let resposta: any;

  if(contato && !contato.completouCadastro())
    resposta = { id: contato.id, nome: contato.nome, telefone: contato.telefone  };

  res.json( Resposta.sucesso(resposta))
});

router.get('/cliente/tel/:tel', async (req: any, res) => {
  const telefone = req.params.tel, empresa = req.empresa;
  let contato: Contato = await new MapeadorDeContato().selecioneSync({ telefone: telefone});
  let resposta: any = {};

  if(contato){
    resposta = { id: contato.id, nome: contato.nome, telefone: contato.telefone,
      email: contato.email,
      cpf: contato.cpf, temConta: contato.senha != null   };

    if(contato.dataNascimento) resposta.dataNascimento = contato.dataNascimento

    if( !contato.completouCadastro())
      resposta.completarCadastro = true;
  }

   if(empresa.integracaoFidelidade)
     await empresa.integracaoFidelidade.setDadosRetornoCadastro( telefone, resposta, empresa)

  JSONUtils.removaNulls(resposta)

  res.json( Resposta.sucesso(resposta))
})

router.get('/cliente/telefone/:tel', async (req: any, res) => {

  const telefone = req.params.tel;

  let contato: Contato = await new MapeadorDeContato().selecioneSync({ telefone: telefone});
  let resposta: any = {};


  if(contato){
    resposta = {
      cliente: { id: contato.id, nome: contato.nome, telefone: contato.telefone, cpf: contato.cpf ,
        cartoes: contato.cartoes
      },
      empresa: req.empresa
    };

    for(let i = 0; i <  contato.cartoes.length ; i++){
      let cartao: any = contato.cartoes[i];
      cartao.plano.brindes =  await new MapeadorDeBrinde().listeAsync({idPlano: cartao.plano.id } )
    }
  }

  res.json(Resposta.sucesso(resposta))
})

router.get('/cliente/telefone/:tel/:token', async (req: any, res) => {

  const telefone = req.params.tel;
  const token = req.params.token;

  let contato: Contato = await new MapeadorDeContato().selecioneSync({ telefone: telefone, token: token});
  let resposta: any = {};

  if(contato){

    resposta = {
      cliente: { id: contato.id, nome: contato.nome, telefone: contato.telefone, cpf: contato.cpf ,
                 cartoes: contato.cartoes
              },
      empresa: req.empresa
    };

    for(let i = 0; i <  contato.cartoes.length ; i++){
      let cartao: any = contato.cartoes[i];
      cartao.plano.brindes =  await new MapeadorDeBrinde().listeAsync({idPlano: cartao.plano.id } )
    }
  }

  res.json(Resposta.sucesso(resposta))
});

router.get('/cliente/:id', (req, res) => {
  //TODO: buscar por TOKEN ao invés de ID
  const id = req.params.id;
  const idEmpresa = (new VariaveisDeRequest()).obtenhaIdEmpresaLogada();
  if (!id)
    return res.json(Resposta.erro("Não foi informado o id do cliente a carregar"));

  if (!idEmpresa)
    return res.json(Resposta.erro("Não foi possível identificar a empresa logada. Verifique a url e tente novamente."));

  const mapeador = new MapeadorDeContato();
  mapeador.selecioneSync({id: id}).then( (contato: Contato) => {
    const mapeadorEmpresa = new MapeadorDeEmpresa();

    mapeadorEmpresa.selecioneSync(idEmpresa).then( (empresa: Empresa) => {
      const mapeadorBrinde = new MapeadorDeBrinde();
      if (!contato) {
        return res.json({sucesso: false,
          erro: {
            mensagem: "Não foi possível encontrar um cliente com o id " + id,
            empresa: empresa
          }
        });
      }

      async.forEachSeries(contato.cartoes, (cartao: any, cb: any) => {
        mapeadorBrinde.listeAsync({idPlano: cartao.plano.id}).then( (brindes: any) => {
          cartao.plano.brindes = brindes;
          cb();

        });
      }, () => {
        res.setHeader("Content-Type", "application/json; charset=utf-8");
        return res.json(Resposta.sucesso({
          empresa: empresa,
          cliente: contato
        }));
      })
    });
  });
});

router.get('/regras/', async (req: any, res) => {
  let empresa = req.empresa;

  let planos = await  new MapeadorDePlano().listeAsync({ idEmpresa: empresa.id})
  let brindes = await new MapeadorDeBrinde().listeAsync( { idEmpresa: empresa.id})

  let regras = RegraUlils.getRegras(empresa, planos , brindes);

  res.json(Resposta.sucesso(regras))

})

router.get('/regras/resumo', async (req: any, res) => {
  let empresa = req.empresa;

  let planos = await  new MapeadorDePlano().listeAsync({ idEmpresa: empresa.id});
  let brindes = await new MapeadorDeBrinde().listeAsync( { idEmpresa: empresa.id});

  let regras = RegraUlils.getRegras(empresa, planos , brindes);

  res.json(Resposta.sucesso(regras.getResumo()))
});


router.get('/contatos', async (req: any, res) => {
  const dtInicio = req.query.di;
  const dtFim = req.query.df;
  const qtdeVendasRecorrente = req.query.qtdeVendasRecorrente;
  const qtdeDiasEmRisco = req.query.qtdeDiasEmRisco;
  const qtdeDiasPerdido = req.query.qtdeDiasPerdido;
  const qtdeVendasVIP = req.query.qtdeVendasVIP;
  const ticketMedioVIP = req.query.ticketMedioVIP;
  const categoria = req.query.categoria;
  const qtdeDiasPeriodo = Number(req.query.qtdeDiasPeriodo);
  const empresa: Empresa = req.empresa;
  // 'VIP'

  if( !dtInicio || !dtFim ) {
    return res.json({
      sucesso: false
    });
  }

  if( !categoria ) {
    return res.json({
      sucesso: false,
      mensagem: 'Informe categoria'
    });
  }

  const hoje = moment();
  let objDataFinal = moment(dtFim, "YYYY-MM-DD hh:mm:ss");

  if( objDataFinal.isAfter(hoje) ) {
    objDataFinal = hoje;
  }

  const dataInicio = moment(dtInicio, "YYYY-MM-DD hh:mm:ss").format('YYYY-MM-DD HH:mm:ss');
  const dataFinal = objDataFinal.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');

  let mapeador: any = new MapeadorDeCartao();

  if( empresa.temModulo(Modulo.Pedido) ) {
    mapeador = new MapeadorDeContato();
  }

  let registros: Array<Contato> = await mapeador.obtenhaClientesRelatorioFidelidade(categoria, dataInicio, dataFinal,
    qtdeVendasRecorrente, qtdeDiasEmRisco, qtdeDiasPerdido, qtdeVendasVIP, ticketMedioVIP, qtdeDiasPeriodo,
              (req.query.r != null ? req.empresa.dadosRede.grupo : null),  req.query.rid );

  res.json({
    sucesso: true,
    data: {
      registros: registros,
      total: registros.length
    }
  });
});

router.get('/relatorioClientes', async (req: any, res) => {
  const dtInicio = req.query.di;
  const dtFim = req.query.df;
  const qtdeVendasRecorrente = req.query.qtdeVendasRecorrente;
  const qtdeDiasEmRisco = req.query.qtdeDiasEmRisco;
  const qtdeDiasPerdido = req.query.qtdeDiasPerdido;
  const qtdeVendasVIP = req.query.qtdeVendasVIP;
  const ticketMedioVIP = req.query.ticketMedioVIP;
  const qtdeDiasPeriodo = Number(req.query.qtdeDiasPeriodo);
  const empresa: Empresa = req.empresa;

  if( !dtInicio || !dtFim ) {
    return res.json({
      sucesso: false
    });
  }

  const hoje = moment();
  let objDataFinal = moment(dtFim, "YYYY-MM-DD hh:mm:ss");

  if( objDataFinal.isAfter(hoje) ) {
    objDataFinal = hoje;
  }

  const dataInicio = moment(dtInicio, "YYYY-MM-DD hh:mm:ss").format('YYYY-MM-DD HH:mm:ss');
  const dataFinal = objDataFinal.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');

  let mapeador: any = new MapeadorDeCartao();

  if( empresa.temModulo(Modulo.Pedido) ) {
    mapeador = new MapeadorDeContato();
  }

  let registros: Array<DadosRelatorioClientesFidelidade> = await mapeador.obtenhaRelatorioFidelidade(dataInicio, dataFinal,
    qtdeVendasRecorrente, qtdeDiasEmRisco, qtdeDiasPerdido, qtdeVendasVIP, ticketMedioVIP, qtdeDiasPeriodo,
    (req.query.r ? req.empresa.dadosRede.grupo : null), req.query.rid );

  const registrosVazios: Array<DadosRelatorioClientesFidelidade> = [];

  registrosVazios.push(new DadosRelatorioClientesFidelidade('Regular'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('Em Risco'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('Novo'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('Novo Importado'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('Recorrente'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('Perdido'));
  registrosVazios.push(new DadosRelatorioClientesFidelidade('VIP'));

  registros = _.union(registros, registrosVazios);

  registros = _.uniq(registros, (item: DadosRelatorioClientesFidelidade, key: number, list: any) => {
    return item.status;
  });

  const relatorio = new RelatorioClientesFidelidade();

  for( let i = 0; i < registros.length; i ++ ) {
    const registro: DadosRelatorioClientesFidelidade = registros[i];

    relatorio.adicione(registro);
  }

  const lista = [];

  // tslint:disable-next-line:forin
  for( const p in relatorio.mapDados ) {
    lista.push(relatorio.mapDados[p]);
  }

  res.json({
    sucesso: true,
    data: {
      registros: lista,
      qtdeClientes: relatorio.qtdeClientes,
      totalGasto: relatorio.totalGasto
    }
  });
});

router.get('/planos-empresariais', async (req: any, res) => {
  let planos = await PlanoEmpresarial.liste()

  res.json(Resposta.sucesso(planos))
});

router.get('/planos-empresariais/admin', async (req: any, res) => {

  let planos = await PlanoEmpresarial.liste()

  planos.forEach((plano: any) => {
    plano.setDescontoPadrao();
    plano.periodoTexto = plano.obtenhaDescricaoPeriodoCobranca();
  });

  //parar exibir planos planos antigos nao usados
  planos = planos.filter((item: PlanoEmpresarial) => item.descontoPadrao >= 0)

  res.json(Resposta.sucesso(planos))
});

router.get('/planos/empresa', async (req: any, res) => {
  let planos = await PlanoEmpresarial.liste( { publico: true})

  res.json(Resposta.sucesso(planos))

});

router.get('/autocomplete', async (req: any, res) => {
  const empresa: Empresa = Ambiente.Instance.contexto().empresa;
  const q = req.query.q;

  new TomtomService().autocomplete(empresa, q).then( (resultados: any) => {
    res.json({
      sucesso: true,
      resultados: resultados
    });
  });
});

router.get('/script/versao', function(req, res, next) {
  res.json({
    sucesso: true,
    versao: 1
  });
});

router.get('/script', function(req, res, next) {
  res.header('Cache-Control', 'private, no-cache, no-store, must-revalidate');
  res.header('Expires', '-1');
  res.header('Pragma', 'no-cache');
  res.type('.js');

  const origin = req.headers.origin;
  const host = req.headers.host;

  console.log('origin: ' + origin);
  console.log('host: ' + host);

  res.render('script_zap.ejs', {urlServidor: 'https://' + host});
});

router.get('/ultimasAcoes', async(req, res) => {
  let query: any = {ultimasAcoes: true, inicio: req.query.i || 0, quantidade: req.query.t || 20};

  query.inicio = Number(query.inicio)
  query.quantidade = Number(query.quantidade)

  if(req.query.cid && Number(req.query.cid) > 0)
    query.idContato = Number(req.query.cid);

  if(req.query.caid && Number(req.query.caid) > 0)
    query.idCartao = Number(req.query.caid);

  if(req.query.sp)
    query.soPontuado = true

  const acoes = await new MapeadorDeAcaoDoContato().listeAsync(query).catch( (erro) => {
    res.json(Resposta.erro(erro.message));
  });

  let lista = [];

  if(acoes)
    lista = acoes.map( (acaoContato: any) => new DTOAcaoContato(acaoContato));

    res.json( Resposta.sucesso(lista));
});

router.get('/pontuacoes/vencer', async (req: any, res: any) => {
  let idCartao = req.query.cid;
  if(!idCartao) return res.json( Resposta.sucesso());

  let dataInicio = new Date();
  let dataFim = moment().add( 90, 'd').toDate();

  if(req.query.di)
    dataInicio = moment(req.query.di).toDate();

  if(req.query.df)
    dataFim = moment(req.query.df).toDate();

  let pontuacoesVencer: any =  await  new MapeadorDePontuacaoRegistrada().listePontuacoesAhVencer(idCartao, dataInicio, dataFim);

  res.json(Resposta.sucesso( new DTOPontuacaoVencer(pontuacoesVencer)));
});

router.get('/estados', async (req: any, res: any) => {
  const amp = req.query['__amp_source_origin'];

  let estados = await new MapeadorDeEstado().listeAsync({});

  if( amp ) {
    for( let i = 0; i < estados.length; i++ ) {
      estados[i].selecionado = false;
    }

    res.json(Resposta.sucesso({estados: estados}))
  } else {
    res.json(Resposta.sucesso(estados))
  }
});

router.get('/vazio', (req: any, res: any) => {
  res.json(Resposta.sucesso([]))
});

router.get('/cidades/:eid', async (req: any, res: any) => {
  let idEstado = req.params.eid;

  if( !idEstado || isNaN(Number(idEstado)) ) {
    res.json(Resposta.sucesso([]))
    return;
  }

  let cidades = await new MapeadorDeCidade().listeAsync({idEstado: Number(idEstado)});

  res.json(Resposta.sucesso(cidades))
})

router.get('/endereco/:cep', async (req: any, res: any) => {
  let cep = req.params.cep;

  const resposta = await new CepService().busque(cep);

  res.json(resposta);
});

router.post('/envieCardapio', async(req: any, res) => {
  let numero = decodeURIComponent(req.query.numero);
  let nome = req.query.nome;
  const empresa: Empresa = Ambiente.Instance.contexto().empresa;

  let telefoneLibGoogle = PhoneNumberUtil.getInstance().parse(numero);
  let tipo = PhoneNumberUtil.getInstance().getNumberType(telefoneLibGoogle);

  if( tipo !== PhoneNumberType.FIXED_LINE && numero.length  < 14 && numero.startsWith("+55")) {
    numero = numero.substr(0, 5) + '9' + numero.substr(5);
  }

  let {codigoPais, telefone} = Contato.extraiCodigoPaisETelefone(numero);

  new MapeadorDeContato().selecioneSync({codigoPais, telefone: telefone}).then( async(contato: any) => {
    if( !contato ) {
      contato = new Contato(null, nome, telefone + '', null, null, null, null, codigoPais);

      contato.empresa = empresa;

      const contatoService = new ContatoService();

      await contatoService.salve(contato);

      if( !contato.id ) {
        res.json(Resposta.erro("Erro ao criar contato."));
        return;
      }
    }

    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    if( !contato.empresa.cardapio ) {
      res.json(Resposta.erro("Empresa não tem o cardápio configurado."));
      return;
    }

    notificacaoService.envieCardapio(contato).then( (resposta: Resposta<any>) => {
      res.json({
        sucesso: true,
        data: {
          resposta: resposta,
          id: contato.id
        }
      });
    });
  });
});

router.post('/envieConfirmacaoPedido', async(req: any, res) => {
   let dados = req.body;

   let pedido: any  = await  new PedidoService().envieConfirmacaoPedido(req.empresa, req.user, dados).catch( erro => {
      res.json(Resposta.erro(erro))
   })

  if (pedido) {
    res.json(Resposta.sucesso(
      {
        mensagem: 'Confirmação de pedido será enviada!',
        contato: pedido.contato, endereco: pedido.endereco
      }));
  }

});


router.post('/contato/loja',  async (req: any, res: any) => {
  const dados = req.body;
  const contato = new Contato(null, dados.nome, dados.telefone, dados.sexo, dados.dataNascimento , dados.cpf, dados.email);

  contato.id = dados.id;

  if(!dados.atualizarCadastro){
    let erroSenha: any = contato.setSenha(dados.senha);

    if(erroSenha) return   res.json(Resposta.erro(erroSenha));
  }


  if(dados.adicionarFidelidadeGcom){
    let erroFidelidade: any;
    let id_cliente: any =
      await new GcomERPService(null).crieClienteFidelidade(req.empresa, contato).catch((erro) => {
        erroFidelidade = erro;
      })

    if(erroFidelidade)
      return res.json(Resposta.erro(erroFidelidade))

  }


  new ContatoService().salve(contato).then( async resp => {
    let dtoContato = await contato.obtenhaDTOContatoLogado(req.empresa);

    req.session.contatoLogado = dtoContato;

    res.json(Resposta.sucesso({id: contato.id}));
  }).catch(erro => {
    res.json(Resposta.erro(erro));
  });
});

router.put('/contato/telefone/valideCodigo',  async (req: any, res) => {
  let dados = req.body;
  let codigo = dados.codigo;
  let idContato = dados.id;

  if(codigo && idContato){
    let contato: Contato = await new MapeadorDeContato().selecioneSync({id: idContato});
    let sessaoDados: any = await CacheService.obtenhaCodigoValidacao(contato);
    if(sessaoDados){
        if(sessaoDados.codigo === codigo){
          if(new Date(sessaoDados.expireIn).getTime() >= new Date().getTime()){

            let dtoContato = await contato.obtenhaDTOContatoLogado(req.empresa);

            req.session.contatoLogado = dtoContato;

            CacheService.insiraCodigoValidacao(contato, null);

            res.json(Resposta.sucesso(dtoContato))
          } else {
            res.json(Resposta.erro('Codigo expirado, gere um novo código'));
          }
        } else {
          res.json(Resposta.erro('Codigo inválido, verifique o codigo informado'));
        }
    } else {
      res.json(Resposta.erro('Não foi possível obter codigo validação usado'));
    }
  } else {
    res.json(Resposta.erro(!idContato ? 'Contato invalido' : 'Codigo não informado'));
  }
})

router.put('/contato/telefone/gereCodigo',  async (req: any, res) => {
  let dados = req.body;
  let contato: any;
  let telefone = dados.tel,
    codigoPais = dados.pais,
    empresa = req.empresa;

  if(dados.id || (telefone && codigoPais)){
    if(dados.id)
     contato = await new MapeadorDeContato().selecioneSync({id: dados.id});
    else
     contato = await new MapeadorDeContato().selecioneLogin({ login: telefone, codigoPais: codigoPais});

    if(contato){
        const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

        let codigo = Token.gereCodigo();

        let resposta: any = await notificacaoService.envieCodigoConfirmacaoConta(contato, codigo);

        if(resposta && resposta.sucesso){
          CacheService.insiraCodigoValidacao(contato, codigo, resposta.data)

          res.json(Resposta.sucesso({id: contato.id }));
        } else {
          res.json(resposta);
        }
      } else {
        res.json(Resposta.erro('Telefone informado não possui cadastro'));
      }
    } else {
      res.json(Resposta.erro('Telefone inválido'));
    }
});

router.put('/contato/loja/:token',  async (req: any, res) => {
  const dados = req.body;
  const contato = new Contato(dados.id, dados.nome, dados.telefone, dados.sexo, null , dados.cpf, dados.email);

  let erroSenha: any = contato.setSenha(dados.senha);

  if(erroSenha) return   res.json(Resposta.erro(erroSenha));

  new ContatoService().atualize(contato, req.empresa).then(resp => {
    res.json(Resposta.sucesso({id: contato.id}));
  }).catch(erro => {
    res.json(Resposta.erro(erro));
  });
});

router.get('/contato/loja/:telefone', async (req: any, res) => {
  let contato = await new MapeadorDeContato().selecioneLogin({ login: req.params.telefone}),
     empresa = req.empresa,
     resposta: any = {};

  if(!contato || !contato.senha || contato.removido){ //retirando validação de conta 24/08 solicitação feita
    resposta.novo = true;
      if(contato && contato.id){
      resposta.id =  contato.id ;
      /*
      resposta.nome =  contato.nome ;
      resposta.cpf =  contato.cpf ;
      resposta.email =  contato.email ;

       */
    }

  } else if(contato){
     resposta.existente = !req.session.contatoLogado || req.session.contatoLogado.id !== contato.id  ;
  }

  if(empresa.integracaoFidelidade && !resposta.existente)
    await empresa.integracaoFidelidade.setDadosRetornoCadastro( req.params.telefone, resposta, empresa)

  res.json(Resposta.sucesso(resposta))
})

router.post('/contato/valide/:token', async (req: any, res: any) => {
  let dadosValidacao =  req.session[req.params.token];
  let codigo = req.body.codigo;

  if(!dadosValidacao)
    return res.json(Resposta.erro("Token não é mais válido."))

  console.log('codigo esperado: ' + dadosValidacao.codigo)
  console.log('codigo enviado: ' + codigo)

  if(!codigo)
    return res.json(Resposta.erro("Código naõ informado"))

  if(codigo.toString() === dadosValidacao.codigo.toString()){
    res.json(Resposta.sucesso());
  } else {
    res.json(Resposta.erro('Código inválido, verifique o codigo informado'));
  }

});


router.get('/validacao/:token', async (req: any, res: any) => {
  let dadosValidacao =  (req.session as any)[req.params.token];

  if(dadosValidacao)
    res.json(Resposta.sucesso({contato: dadosValidacao.contato, meioDeEnvio: dadosValidacao.meioDeEnvio}));
  else
    res.json(Resposta.erro('Token inválido'));
});


router.get('/enderecos/me', async (req: any, res: any) => {
  let contato = req.session.contatoLogado;
  const empresa = req.empresa;

  if(contato && contato.id){
    let enderecos: Array<Endereco> = await new MapeadorDeEndereco().listeAsync({idContato: contato.id});

    for( let endereco of enderecos ) {
      endereco.verifiqueSeEstaCompleto(empresa);
    }
    res.json(Resposta.sucesso(enderecos))
  } else {
    res.json(Resposta.sucesso([]))
  }
})


router.post('/fidelidade/gcom/optin' , async (req: any, res: any) => {
  let dadosContato = req.body,
    contato = req.session.contatoLogado,
    empresa = req.empresa;

  let gcomService: GcomERPService = new GcomERPService(null),
    mapeadorDeContato = new MapeadorDeContato();

  if(empresa.integracaoFidelidade && empresa.integracaoFidelidade.ehGcom()){
    if(dadosContato.nome !== contato.nome){
      contato.nome = dadosContato.nome;
      await mapeadorDeContato.atualizeCadastro(contato)
    }

    if(dadosContato.cpf && dadosContato.cpf !== contato.cpf){
      let existeComCpf = await  mapeadorDeContato.existeSync( {cpf: dadosContato.cpf});

      if(existeComCpf)
        return  res.json(Resposta.erro('CPF já está em uso: ' + dadosContato.cpf))

      contato.cpf = dadosContato.cpf;
      await new MapeadorDeContato().atualizeCadastro(contato)
    }

    if(dadosContato.email && dadosContato.email !== contato.email){
      let existe  = await  mapeadorDeContato.existeSync( {email: dadosContato.email});

      if(existe)
        return  res.json(Resposta.erro('Email já está em uso: ' + dadosContato.email))

      contato.email = dadosContato.email;
      await new MapeadorDeContato().atualizeEmail(contato);
    }

    if(dadosContato.fazerOptinUsarSaldo){ // atualizar um cadastro existente
      let dadosFidelidade: any = await gcomService.obtenhaSaldoCashback(empresa, contato.telefone)

      if(!dadosFidelidade || !dadosFidelidade.id_cliente)
         return   res.json(Resposta.erro(String(`Telefone "${contato.telefone}" não está cadastrado no programa de fidelidade`)))

      if(dadosFidelidade.cpf && dadosFidelidade.cpf !== dadosContato.cpf )
        return   res.json(Resposta.erro(String(`O CPF "${dadosContato.cpf}" não está associado a seu telefone de cadastro`)))

      let dadosCashback: any =
        await gcomService.atualizeClienteFidelidade(dadosFidelidade.id_cliente, empresa, contato).catch((erro) => {
          console.log(erro)
          res.json(Resposta.erro(erro))
        });

      if(dadosCashback)
        res.json(Resposta.sucesso())
    } else {
      let dadosCashback: any =
        await gcomService.crieClienteFidelidade(empresa, contato).catch((erro) => {
          console.log(erro)
          res.json(Resposta.erro(erro))
        });

      if(dadosCashback)
        res.json(Resposta.sucesso())
    }
  } else {
    res.json(Resposta.erro('Fidelidade não está ativa na empresa'))
  }

})

router.get('/resgate/saldo' , async (req: any, res: any) => {
  let contato = req.session.contatoLogado,
    empresa = req.empresa;

  let dados: any = {
      saldo: 0,
      usarPadrao: true,  // por padrao  vem marcado para usar como pagamento
      podeUsar: true,  // tem valor minimo necessario para usar o saldo
      minimo: 0,  //minimo acumulado para usar foi atingido
      minimoPedido: 0,  //valor minimo do pedido no carrinho foi atigindo
      podeUsarNoPedido: false,
      podeUsarNaLoja: true
  }


  if(empresa.integracaoFidelidade && empresa.integracaoFidelidade.ehGcom()){

    if(!contato || !contato.id || contato.fezLoginGuest){
      dados.fazerLogin = true;
      return res.json(Resposta.sucesso(dados))
    }


    dados.cashbackExterno  = {
      sistema: empresa.integracaoFidelidade.sistema
    }

    if(contato && contato.id && !contato.cpf){
      dados.atualizarCadastro = true;
      return res.json(Resposta.sucesso(dados))
    }

    let telefone = contato  ?  contato.telefone : null;

    let dadosCashback: any =
      await new GcomERPService(null).obtenhaSaldoCashback(empresa, telefone).catch((erro) => {
        console.log(erro)
        res.json(Resposta.erro(erro))
    });

    if(dadosCashback){
      if(dadosCashback.id_cliente > 0){
        let elegivelResgaste = dadosCashback.cadastrado === 'S';

        if(elegivelResgaste){
          dados.saldo =  dadosCashback.valor_cashback_saldo;
          dados.usarPadrao = false; // nao ir marcado para usar

          if(!dadosCashback.cpf || dadosCashback.cpf === contato.cpf){
            dados.podeUsarNoPedido = true;
            empresa.integracaoFidelidade.setDadosCliente(dados.cashbackExterno, dadosCashback)
          } else {
            console.log(String(`CPF GCOM -> ${dadosCashback.cpf} nao bate com cpf cadastrado no promokit -> ${contato.cpf}`))

            ///nao esta exibindo na tela do progrma, testa e barrar seguid com pagamento
            dados.erroFidelidade = String(`Telefone está vinculado a outro cpf`)
          }
        } else {
            dados.podeUsarNoPedido = false;
            dados.fazerOptinUsarSaldo = true;
        }
      } else {
        dados.fazerOptin = true;
      }
      res.json(Resposta.sucesso(dados))
    }
  } else {
      let plano = empresa.integracaoPedidoFidelidade ? empresa.integracaoPedidoFidelidade.plano : null;

      if(plano){
        let idPlano = empresa.integracaoPedidoFidelidade.plano.id;

        if(!plano.acumulaReais())
          dados.resgate = true;

        if(contato && contato.id && !contato.fezLoginGuest){
          let cartao = await new MapeadorDeCartao().selecioneSync({ idContato: contato.id, idPlano: Number(idPlano) });

          if(cartao){
            dados.cartaoid = cartao.id;
            dados.podeUsarNoPedido = true;
            dados.saldo = cartao.pontos;
            dados.minimoPedido = cartao.plano.valorMinimoResgate;
            if( cartao.acumulaReais()){
              let brinde: Brinde =  await new MapeadorDeBrinde().selecioneSync({ idPlano: cartao.plano.id });
              if(brinde)
                dados.minimo = brinde.valorEmPontos;
              dados.podeUsar = dados.saldo >= dados.minimo;
            }
          }
        }  else {
          dados.fazerLogin = true;
          dados.podeUsarNaLoja = true;
        }
      } else {
        dados.podeUsarNaLoja = false;
      }

      res.json(Resposta.sucesso(dados))
    }
})

router.get('/cartao/saldo' , async (req: any, res: any) => {
  let contato = req.session.contatoLogado;
  let idPlano = req.query.pid;
  let resposta: any  = { saldo: 0, minimo: 0 , minimoPedido: 0};

  if(contato && contato.id && idPlano){
    let cartao = await new MapeadorDeCartao().selecioneSync({ idContato: contato.id, idPlano: Number(idPlano) });

    //so retornar saldo de casback para loja
    if(cartao && cartao.acumulaReais()){
      resposta.minimoPedido = cartao.plano.valorMinimoResgate
      resposta.saldo = cartao.pontos;
      let brinde: Brinde =  await new MapeadorDeBrinde().selecioneSync({ idPlano: cartao.plano.id });
      if(brinde)
        resposta.minimo = brinde.valorEmPontos;
    }
  }

  res.json(Resposta.sucesso(resposta));
});

router.get('/contratos/:codigo', async (req: any, res: any) => {
  let codigo = req.params.codigo;

  let contrato = await new MapeadorDeContrato().selecioneSync({ codigo: codigo})

  res.json(Resposta.sucesso(new DTOContrato(contrato)))

})

router.get('/contratos/fatura/blackfriday', async (req: any, res: any) => {
  let empresa = req.empresa;

  let diaPromocao: Date = moment('20231124').startOf('day').toDate();
  let vencimento = moment('20231127').endOf('day').toDate();
  let valorTotalPromocao = 120000;

  let iuguService = new IuguService();

  if(empresa.codigoCliente){
    let faturas: any = await iuguService.obtenhaFaturas(empresa.codigoCliente, diaPromocao);

    // faturas = faturas.filter((fatura: any) => fatura.status === 'paid' || fatura.status === 'pending');
    let faturaPromocao = faturas.find((fatura: any) => fatura.total_cents === valorTotalPromocao);

    if(!faturaPromocao){
      let itens: any = [{descricao: 'Anuidade Promocional Black Friday', qtde: 1, total: valorTotalPromocao / 100}];
      let formasPagamento: any = ['pix', 'credit_card'];

      if(!moment().isBefore(moment(vencimento).startOf('day'), "days"))
        return res.json(Resposta.erro('Promoção encerrada'))

      faturaPromocao = await iuguService.crieFaturaAvulsa(empresa, itens, formasPagamento, vencimento, 12, 'blackfriday')
    }

    let faturaAvulsa =   await new ContratoService().sincronizeFaturaIugu(faturaPromocao, null, empresa, true);

    res.json(Resposta.sucesso(new DTOFatura(faturaAvulsa)))

  } else {
    res.json(Resposta.erro('Empresa sem codigo no Iugu'))
  }

})


router.get('/cloudfare/a/:dominio', (req: any, res: any ) => {
  let hostname: string = req.hostname
  let dominio = req.params.dominio
  if(hostname === 'localhost') hostname = 'winecloud.meucardapio.ai'

  let api
  if(hostname.indexOf('meucardapio') >= 0)  api = ApiCloudfare.getApiMeuCardapio()
  else api = ApiCloudfare.getApiPromokit()

  api.obtenhaRegistroA(dominio).then((resposta) => {
    res.json(resposta)
  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })
})


router.put('/cloudfare/a/:dominio', (req: any, res: any) => {
  let hostname: string = req.hostname
  let dominio = req.params.dominio
  let ip = req.body.ip
  if(hostname === 'localhost') hostname = 'winecloud.meucardapio.ai'

  if(!ip)
    return res.json(Resposta.erro("É necessário informar o novo IP para o registro a"))

  let api
  if(hostname.indexOf('meucardapio') >= 0)  api = ApiCloudfare.getApiMeuCardapio()
  else api = ApiCloudfare.getApiPromokit()

  api.consulteEAtualizeRegistroA(dominio, ip).then((resposta) => {
    res.json(resposta)
  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })
})



router.post('/cloudfare/a/:dominio', (req: any, res: any) => {
  let hostname: string = req.hostname
  let dominio = req.params.dominio
  let ip = req.body.ip
  if(hostname === 'localhost') hostname = 'winecloud.meucardapio.ai'

  if(!ip)
    return res.json(Resposta.erro("É necessário informar o novo IP para o registro a"))

  let api
  if(hostname.indexOf('meucardapio') >= 0)  api = ApiCloudfare.getApiMeuCardapio()
  else api = ApiCloudfare.getApiPromokit()

  api.insiraRegistroA(dominio, ip).then((resposta) => {
    res.json(resposta)
  }).catch((erro) => {
    res.json(Resposta.erro(erro))
  })
})

router.post('/vinculeDominios/gendai', (req: any, res: any) => {
  let hostnames = [
    [615, "iguatemibrasilia.gendai-zap.com.br"],
    [738, "iguatemicampinas.gendai-zap.com.br"],
    [708, "moocaplazashoppping.gendai-zap.com.br"],
    [803, "parqueshoppingmaia.gendai-zap.com.br"],
    [723, "saobernardoplazashopping.gendai-zap.com.br"],
    [707, "senamadureira.gendai-zap.com.br"],
    [653, "shoppingfreicaneca.gendai-zap.com.br"],
    [643, "shoppingjardimsul.gendai-zap.com.br"],
    [614, "shoppingwestplaza.gendai-zap.com.br"]
  ]

  let mapeador = new MapeadorDeEmpresa()
  let promises = []
  for(let dadosHostname of hostnames) {
    let promise = new Promise((resolve: any, reject: any) => {
      let id = dadosHostname[0]
      mapeador.selecioneSync({id: id}).then((empresa: Empresa) => {
        if(!empresa) {
          console.log("Não foi encontrada empresa com id " + id)
          return resolve("Não foi encontrada empresa com id " + id)
        }

        let hostname: any = dadosHostname[1]

        let novoDominio = new DominioDaEmpresa(hostname, true, false, empresa)

        mapeador.atualizeLinkDaEmpresa(empresa, novoDominio).then(() => {
          console.log('O link ' + hostname + ' foi associado à empresa '  + empresa.nome)
          return resolve('O link ' + hostname + ' foi associado à empresa '  + empresa.nome)
        })

      })
    })

    promises.push(promise)
  }

  Promise.all(promises).then((respostas) => {
    res.json(respostas)
  }).catch(reason => {
    res.json(reason)
  })

})

router.post('/vinculeDominios/chinainbox', (req: any, res: any) => {
  let hostnames = [
    [721, "aclimacao.chinainbox-zap.com.br"],
    [678, "aguaverde.chinainbox-zap.com.br"],
    [441, "aguasclaras.chinainbox-zap.com.br"],
    [672, "ahu.chinainbox-zap.com.br"],
    [679, "aldeota.chinainbox-zap.com.br"],
    [788, "alphaville.chinainbox-zap.com.br"],
    [621, "altodaxv.chinainbox-zap.com.br"],
    [610, "aracaju.chinainbox-zap.com.br"],
    [616, "araraquara.chinainbox-zap.com.br"],
    [680, "asanorte.chinainbox-zap.com.br"],
    [413, "asasul2.chinainbox-zap.com.br"],
    [789, "atibaia.chinainbox-zap.com.br"],
    [607, "balneariocamboriu.chinainbox-zap.com.br"],
    [702, "bancarioslondrina.chinainbox-zap.com.br"],
    [695, "barradatijuca.chinainbox-zap.com.br"],
    [741, "bauru.chinainbox-zap.com.br"],
    [677, "bigorrilho.chinainbox-zap.com.br"],
    [766, "bixiga.chinainbox-zap.com.br"],
    [558, "boaviagem.chinainbox-zap.com.br"],
    [693, "botafogo.chinainbox-zap.com.br"],
    [629, "buritis.chinainbox-zap.com.br"],
    [779, "cambuicampinas.chinainbox-zap.com.br"],
    [735, "campogrande.chinainbox-zap.com.br"],
    [635, "carapicuiba.chinainbox-zap.com.br"],
    [568, "caxanga.chinainbox-zap.com.br"],
    [748, "caxiasdosul.chinainbox-zap.com.br"],
    [662, "chacarasantoantonio.chinainbox-zap.com.br"],
    [628, "cidadenova.chinainbox-zap.com.br"],
    [582, "copacabana.chinainbox-zap.com.br"],
    [664, "cuiaba.chinainbox-zap.com.br"],
    [630, "epitacio.chinainbox-zap.com.br"],
    [417, "estancia.chinainbox-zap.com.br"],
    [654, "fatima.chinainbox-zap.com.br"],
    [639, "floresta.chinainbox-zap.com.br"],
    [720, "florianopolis.chinainbox-zap.com.br"],
    [668, "freguesiadoo.chinainbox-zap.com.br"],
    [403, "goiania.chinainbox-zap.com.br"],
    [634, "granjaviana.chinainbox-zap.com.br"],
    [692, "gruta.chinainbox-zap.com.br"],
    [440, "guara2.chinainbox-zap.com.br"],
    [649, "guaruja.chinainbox-zap.com.br"],
    [712, "guarulhos.chinainbox-zap.com.br"],
    [749, "guarulhos2.chinainbox-zap.com.br"],
    [686, "higienopolis.chinainbox-zap.com.br"],
    [608, "icarai.chinainbox-zap.com.br"],
    [733, "ilhadogovernador.chinainbox-zap.com.br"],
    [737, "indaiatuba.chinainbox-zap.com.br"],
    [675, "itaim.chinainbox-zap.com.br"],
    [665, "itajai.chinainbox-zap.com.br"],
    [726, "itaquera.chinainbox-zap.com.br"],
    [697, "jacarepagua.chinainbox-zap.com.br"],
    [696, "juizdefora.chinainbox-zap.com.br"],
    [681, "lagonorte.chinainbox-zap.com.br"],
    [613, "lapa.chinainbox-zap.com.br"],
    [645, "laranjeiras.chinainbox-zap.com.br"],
    [492, "limeira.chinainbox-zap.com.br"],
    [660, "marilia.chinainbox-zap.com.br"],
    [727, "maua.chinainbox-zap.com.br"],
    [524, "meier.chinainbox-zap.com.br"],
    [718, "mogidascruzes.chinainbox-zap.com.br"],
    [673, "mooca.chinainbox-zap.com.br"],
    [605, "olinda.chinainbox-zap.com.br"],
    [658, "osasco2.chinainbox-zap.com.br"],
    [636, "parquesaodomingos.chinainbox-zap.com.br"],
    [674, "parquesaolucas.chinainbox-zap.com.br"],
    [701, "penha.chinainbox-zap.com.br"],
    [740, "pinheiros.chinainbox-zap.com.br"],
    [785, "piracicaba.chinainbox-zap.com.br"],
    [725, "piratininga.chinainbox-zap.com.br"],
    [585, "pompeia.chinainbox-zap.com.br"],
    [750, "pontagrossa.chinainbox-zap.com.br"],
    [667, "pontanegra.chinainbox-zap.com.br"],
    [651, "pontaverde.chinainbox-zap.com.br"],
    [423, "portovelho.chinainbox-zap.com.br"],
    [688, "praia.chinainbox-zap.com.br"],
    [713, "recreio.chinainbox-zap.com.br"],
    [606, "rioclaro.chinainbox-zap.com.br"],
    [452, "rosarinho.chinainbox-zap.com.br"],
    [633, "santanadeparnaiba.chinainbox-zap.com.br"],
    [744, "santos.chinainbox-zap.com.br"],
    [648, "saocarlos.chinainbox-zap.com.br"],
    [646, "saocristovao.chinainbox-zap.com.br"],
    [736, "saojose.chinainbox-zap.com.br"],
    [652, "saojosedoscampos.chinainbox-zap.com.br"],
    [618, "saoluis.chinainbox-zap.com.br"],
    [717, "saomiguel.chinainbox-zap.com.br"],
    [722, "saudeipiranga.chinainbox-zap.com.br"],
    [641, "savassi.chinainbox-zap.com.br"],
    [666, "shoppingtaboao.chinainbox-zap.com.br"],
    [757, "sorocaba.chinainbox-zap.com.br"],
    [719, "suzano.chinainbox-zap.com.br"],
    [519, "taubate.chinainbox-zap.com.br"],
    [661, "tijuca.chinainbox-zap.com.br"],
    [659, "tirol.chinainbox-zap.com.br"],
    [676, "valinhos.chinainbox-zap.com.br"],
    [669, "viladapenha.chinainbox-zap.com.br"],
    [715, "vilamariana.chinainbox-zap.com.br"],
    [716, "vitoria.chinainbox-zap.com.br"],
    [704, "zonanorte.chinainbox-zap.com.br"]
  ]

  let mapeador = new MapeadorDeEmpresa()
  let promises = []
  for(let dadosHostname of hostnames) {
    let promise = new Promise((resolve: any, reject: any) => {
      let id = dadosHostname[0]
      mapeador.selecioneSync({id: id}).then((empresa: Empresa) => {
        if(!empresa) {
          console.log("Não foi encontrada empresa com id " + id)
          return resolve("Não foi encontrada empresa com id " + id)
        }

        let hostname: any = dadosHostname[1]

        let novoDominio = new DominioDaEmpresa(hostname, true, false, empresa)

        mapeador.atualizeLinkDaEmpresa(empresa, novoDominio).then(() => {
          console.log('O link ' + hostname + ' foi associado à empresa '  + empresa.nome)
          return resolve('O link ' + hostname + ' foi associado à empresa '  + empresa.nome)
        });
      });
    })

    promises.push(promise)
  }

  Promise.all(promises).then((respostas) => {
    res.json(respostas)
  }).catch(reason => {
    res.json(reason)
  })

})

router.post('/cloudflare/gendai', (req: any, res: any) => {
  let hostname = 'gendai-zap.com.br'

  let dominios = [
    "iguatemibrasilia",
"iguatemicaminas",
"moocaplazashoppping",
"parqueshoppingmaia",
"saobernardoplazashopping",
"senamadureira",
"shoppingfreicaneca",
"shoppingjardimsul",
"shoppingtambore",
"shoppingwestplaza"
  ]

  let ip = '**************'
  let api = ApiCloudfare.getApiGendaiZap()

  let promises = []

  for(let dominio of dominios) {
    let promise = new Promise((resolve, reject) => {

      api.insiraRegistroA(dominio, ip).then((resposta) => {
        resolve(resposta)
      }).catch((erro) => {
        reject(Resposta.erro(erro))
      })

    })

    promises.push(promise)
  }

  Promise.all(promises).then((respostas: any) => {
    res.json(respostas)
  }).catch(reason => {
    res.json(reason)
  })

})


router.post('/cloudflare/chinainbox', (req: any, res: any) => {
  let hostname = 'chinainbox-zap.com.br'

  let dominios = [
    "aclimacao",
    "aguaverde",
    "aguasclaras",
    "ahu",
    "aldeota",
    "alphaville",
    "altodaxv",
    "aracaju",
    "araraquara",
    "asanorte",
    "asasul2",
    "atibaia",
    "balneariocamboriu",
    "bancarioslondrina",
    "barradatijuca",
    "bauru",
    "bigorrilho",
    "bixiga",
    "boaviagem",
    "botafogo",
    "buritis",
    "cambuicampinas",
    "campogrande",
    "carapicuiba",
    "caxanga",
    "caxiasdosul",
    "chacarasantoantonio",
    "cidadenova",
    "copacabana",
    "cuiaba",
    "epitacio",
    "estancia",
    "fatima",
    "floresta",
    "florianopolis",
    "freguesiadoo",
    "goiania",
    "granjaviana",
    "gruta",
    "guara",
    "guaruja",
    "guarulhos",
    "guarulhos2",
    "higienopolis",
    "icarai",
    "ilhadogovernador",
    "indaiatuba",
    "itaim",
    "itajai",
    "itaquera",
    "jacarepagua",
    "juizdefora",
    "lagonorte",
    "lapa",
    "laranjeiras",
    "limeira",
    "marilia",
    "maua",
    "meier",
    "mogidascruzes",
    "mooca",
    "olinda",
    "osasco2",
    "parquesaodomingos",
    "parquesaolucas",
    "penha",
    "pinheiros",
    "piracicaba",
    "piratininga",
    "pompeia",
    "pontagrossa",
    "pontanegra",
    "pontaverde",
    "portovelho",
    "praia",
    "recreio",
    "rioclaro",
    "rosarinho",
    "santanadeparnaiba",
    "santos",
    "saocarlos",
    "saocristovao",
    "saojose",
    "saojosedoscampos",
    "saoluis",
    "saomiguel",
    "saudeipiranga",
    "savassi",
    "shoppingtaboao",
    "sorocaba",
    "suzano",
    "taguatinga",
    "taubate",
    "tijuca",
    "tirol",
    "valinhos",
    "viladapenha",
    "vilamariana",
    "vitoria",
    "zonanorte"
  ]

  let ip = '**************'
  let api = ApiCloudfare.getApiChinaInBoxZap()

  let promises = []

  for(let dominio of dominios) {
    let promise = new Promise((resolve, reject) => {

      api.insiraRegistroA(dominio, ip).then((resposta) => {
        resolve(resposta)
      }).catch((erro) => {
        reject(Resposta.erro(erro))
      })

    })

    promises.push(promise)
  }

  Promise.all(promises).then((respostas: any) => {
    res.json(respostas)
  }).catch(reason => {
    res.json(reason)
  })

})

/*
"aclimacao",
"aguaverde",
"aguasclaras",
"ahu",
"aldeota",
"alphaville",
"altodaxv",
"aracaju",
"araraquara",
"asanorte",
"asasul2",
"atibaia",
"balneariocamboriu",
"bancarioslondrina",
"barradatijuca",
"bauru",
"bigorrilho",
"bixiga",
"boaviagem",
"botafogo",
"buritis",
"cambuicampinas",
"campogrande",
"carapicuiba",
"caxanga",
"caxiasdosul",
"chacarasantoantonio",
"cidadenova",
"copacabana",
"cuiaba",
"epitacio",
"estancia",
"fatima",
"floresta",
"florianopolis",
"freguesiadoo",
"goiania",
"granjaviana",
"gruta",
"guara",
"guaruja",
"guarulhos",
"guarulhos2",
"higienopolis",
"icarai",
"ilhadogovernador",
"indaiatuba",
"itaim",
"itajai",
"itaquera",
"jacarepagua",
"juizdefora",
"lagonorte",
"lapa",
"laranjeiras",
"limeira",
"marilia",
"maua",
"meier",
"mogidascruzes",
"mooca",
"olinda",
"osasco2",
"parquesaodomingos",
"parquesaolucas",
"penha",
"pinheiros",
"piracicaba",
"piratininga",
"pompeia",
"pontagrossa",
"pontanegra",
"pontaverde",
"portovelho",
"praia",
"recreio",
"rioclaro",
"rosarinho",
"santanadeparnaiba",
"santos",
"saocarlos",
"saocristovao",
"saojose",
"saojosedoscampos",
"saoluis",
"saomiguel",
"saudeipiranga",
"savassi",
"shoppingtaboao",
"sorocaba",
"suzano",
"taguatinga",
"taubate",
"tijuca",
"tirol",
"valinhos",
"viladapenha",
"vilamariana",
"vitoria",
"zonanorte"
 */
router.post('/cloudfare/migreTodos', (req: any, res: any) => {
  let ip = req.body.ip

  if(!ip) return res.json({
    sucesso: false,
    mensagem: 'É necessário informar o ip de destino da migração'
  })

  console.log('[CLOUDFARE] ******** INICIANDO MIGRAÇÃO DE DOMÍNIO ********')
  console.log('[CLOUDFARE] Ip de destino: ' + ip)

  new MapeadorDeEmpresa().selecioneTodosDominiosAtivos().then((empresas: Empresa[]) => {
    console.log("[CLOUDFARE] Empresas para migração: " + empresas.length)
    ExecutorAsync.execute( async (callback: Function) => {
      let apiCloudfare = ApiCloudfare.getApiMeuCardapio()

      apiCloudfare.atualizeOuInsiraRegistroA(empresas, ip).then((resultado: any) => {
        if(!resultado.sucesso)  console.log("[CLOUDFARE] " + resultado.data.length +
          " empresas tiveram o registro A processado e atualizado para o IP " + ip)

        console.log("[CLOUDFARE] ******* Processo de migração encerrado *******")
        callback();
      })

    }, (erro: any) => {
      console.log("Ocorreu um erro ao importar: " + erro)
    }, 100)

    res.json({
      sucesso: true,
      data: "O processo de migração foi iniciado com sucesso"

    })
  })
});

router.get('/mesas/nome/:nome', (req: any, res: any) => {
  new MapeadorDeMesa().selecioneSync({nome: req.params.nome}).then( (mesa: any) => {
    res.json( Resposta.sucesso(mesa));
  });
});

router.get('/mesas/:id', (req: any, res: any) => {

  new MapeadorDeMesa().selecioneSync(Number(req.params.id)).then( (mesa: any) => {
    res.json( Resposta.sucesso(mesa));
  });
})

router.get('/mesas/hash/:hashMesa', (req: any, res: any) => {
  let hashMesa = req.params.hashMesa

  if(!hashMesa) return res.json(Resposta.erro("É necessário informar o hash da mesa"));

  new MapeadorDeSessaoMesa().selecioneSync({hash: hashMesa}).then((sessaoMesa: SessaoMesa) => {
    if(!sessaoMesa)
      return res.json(Resposta.erro("Não foi encontrada uma sessão com o hash " + hashMesa))

    if(sessaoMesa.expirada)
      return res.json(Resposta.erro("A sessão com hash " + hashMesa + " está expirada."))



    const agora = moment();
    let horarioSessao = moment(sessaoMesa.horario);

    let duration = moment.duration(agora.diff(horarioSessao));
    let diferencaEmHoras = duration.asHours();

    if(diferencaEmHoras > 6) {
      let mapeador = new MapeadorDeSessaoMesa()
      mapeador.transacao((conexao: any, commit: any) => {
        mapeador.marqueComoExpirada(sessaoMesa).then(() => {
          commit(() => {
            return res.json(Resposta.erro("A sessão com hash " + hashMesa + " está expirada."))
          })

        }).catch((erro: any) => {
          conexao.rollback(() => {
            return res.json(Resposta.erro("Houve um erro ao marcar a sessão como expirada: " + erro))
          })
        })

      })
    } else res.json(Resposta.sucesso(sessaoMesa.mesa))
  })
})

router.get('/tokenCielo', (req: any, res: any) => {
  const dadosCielo = {
    ClientID: 'f9769e61-0144-4be3-b821-8a6ab84f719d',
    ClientSecret: 'GGfnYVvErXJAP9MExubI5cwH6UxWADZtUA2898EGcV0='
  };

  const base64 = dadosCielo.ClientID + ":" + dadosCielo.ClientSecret;

  axios.post('https://cieloecommerce.cielo.com.br/api/public/v2/token', {

  }, {
    headers: {"Authorization": "Basic " + Buffer.from(base64).toString('base64')}
  }).then( (response: any) => {
    res.json({
      sucesso: true,
      data: response.data
    });
  });
});

router.get('/banners', (req: any, res: any) => {
  if( !req.empresa ) {
    res.json(Resposta.erro('Não tem empresa logada!'));
    return;
  }

  const mapeadorDeBanner = new MapeadorDeBanner();

  mapeadorDeBanner.listeAsync({}).then( (banners) => {
    banners = banners.filter((banner: Banner) => banner.estaAtivo())

    res.json(Resposta.sucesso(banners));
  });
});

router.post('/insiraToken', async(req, res) => {
  const dados = req.body;

  console.log('insiratoken: ', dados);

  const tokenGcm = new TokenGcm();
  Object.assign(tokenGcm, dados);

  const mapeador = new MapeadorDeTokenGcm();

  const tokenAnterior = mapeador.selecioneSync({token: tokenGcm.token});

  if( tokenAnterior ) {
    res.json(Resposta.sucesso({
      mensagem: 'Token atualizado com sucesso'
    }));
    return;
  }

  mapeador.insiraGraph(tokenGcm).then( (resposta: any) => {
    res.json(Resposta.sucesso({
      mensagem: 'Token criado com sucesso'
    }));
  });
});

/*
router.get('/buscarFoto', async (req: any,  res: any) => {
  const ean = req.query.e;

  if( !ean ) {
    return res.json(Resposta.erro('Ean é obrigatório'));
  }

  const mapeador = new MapeadorDeImagemCodigoDeBarras();

  mapeador.listeAsync({semImagem: true}).then( async (imagens: Array<ImagemEan>) => {
    const buscador = new BuscadorDeFotosEANService();
    for( let index = 0; index < imagens.length; index++ ) {
      const imagem = imagens[index];

      if (index % 10 === 0) {
        console.log('Indice: ' + index);
      }

      const resposta = await buscador.baixeFoto(imagem.codigoDeBarras);
    }

    res.json(Resposta.sucesso("Baixou"));
  });
});
 */

router.get('/testeAssinatura', (req: any, res: any) => {
  let mapeador = new MapeadorDeFatura()

  mapeador.selecioneSync({id: 508}).then ((fatura) => {
    if(!fatura)
      return res.json(Resposta.erro('Não foi encontrada fatura com o id 508'))

    fatura.status = EnumStatusFatura.Paga
    let nota = new NotaFiscalDeServico(fatura, 1000)

    let xml = nota.gereXml(true)
    let xmlAssinado = AssinadorDeXml.assine(xml);

    new InvocadorSoapNfseGoiania('https://nfse.goiania.go.gov.br/ws/nfse.asmx').invoqueWebService(xmlAssinado).then((resposta) => {
      res.json(Resposta.sucesso( {
        xmlAssinado: xmlAssinado,
        resposta: resposta
      }))
    })

  })
})

router.post('/dupliqueCatalogoDaRede/:rede/:idCatalogo', async(req: any, res: any) => {
  let rede = req.params.rede;
  let idCatalogo = req.params.idCatalogo

  let mapeadorDeCatalogo = new MapeadorDeCatalogo()

  mapeadorDeCatalogo.selecioneSync({id: idCatalogo, rede: rede}).then(async (catalogo: Catalogo) => {
    if(!catalogo)
      return res.json(Resposta.erro('Não foi encontrado catálogo com id ' + idCatalogo + ' e rede ' + rede))

   catalogo.disponibilidades = await new MapeadorDeDisponibilidade( catalogo).listeAsync({idCatalogo:  catalogo.id});

    let novoCatalogo = new Catalogo();
    Object.assign(novoCatalogo, catalogo);
    novoCatalogo.criacao = new Date();
    novoCatalogo.atualizacao = new Date();
    novoCatalogo.nome = catalogo.nome + ' - Cópia';

    mapeadorDeCatalogo.insiraSync(novoCatalogo).then(() => {
      if(!novoCatalogo.id)
        return res.json(Resposta.erro('Não foi possível criar o novo catálogo'))

      let importador = new ImportadorProduto();

      importador.copieProdutos(catalogo, novoCatalogo, req.user).then(async (resposta) => {
        let catalogoDaRede = new CatalogoDaRede()

        catalogoDaRede.catalogo = novoCatalogo
        catalogoDaRede.rede = rede

        let mapeadorDeCatalogoDaRede = new MapeadorDeCatalogoDaRede()

        mapeadorDeCatalogoDaRede.insiraSync(catalogoDaRede).then(() => {
          return res.json(Resposta.sucesso(resposta))
        })
      })
    })
  })
})

router.get('/copieProdutos/:idOrigem/:idDestino', async(req: any, res: any) => {
  let idOrigem = req.params.idOrigem
  let idDestino = req.params.idDestino

  let mapeadorDeEmpresa = new MapeadorDeEmpresa()

  mapeadorDeEmpresa.selecioneSync({id: idOrigem}).then((empresaOrigem: Empresa) => {
    if(!empresaOrigem)
      return res.json('Não localizamos a empresa com id: ' + idOrigem)

    mapeadorDeEmpresa.selecioneSync({id: idDestino}).then(async (empresaDestino: Empresa) => {
      if(!empresaDestino)
        return res.json('Não localizamos a empresa com id:' + idDestino)

      let importador = new ImportadorProduto()

      importador.copieProdutos(empresaOrigem.catalogo, empresaDestino.catalogo, req.user).then( async (resposta) => {
        await new MapeadorDeEmpresa().removaDasCaches(empresaDestino, true);
        return res.json(Resposta.sucesso(resposta))
      })
    })
  })

})

router.get('/testeTarefaDeEnvioDeNFse', async (req: any, res: any) => {
  let mapeador = new MapeadorDeFatura()

  mapeador.selecioneSync({id: 455}).then (async (fatura) => {
    if(!fatura)
      return res.json(Resposta.erro('Não foi encontrada fatura com o id 508'))

    fatura.status = EnumStatusFatura.Paga

    let respostaCriacaoTarefa = await TarefaDeEnvioDeNfse.crieTarefaDeEnvioDeNota(fatura)

    if(!respostaCriacaoTarefa.sucesso)
      return res.json(Resposta.erro(respostaCriacaoTarefa.erro))

    let tarefa = respostaCriacaoTarefa.data as TarefaDeEnvioDeNfse

    return res.json(await tarefa.execute(false))

  });
})

router.get('/testeNotificacaoCampoCupom', async(req: any, res: any) => {
  const empresa = req.empresa;
  const contato = new Contato(1, 'Fulano de Tal', '62982301144');

  const notificacao = new Notificacao();

  notificacao.mensagem = `Campanha

LINK: [CUPOM_dd15OFF]

teste
[NomeContato]!`

  const contexto = {};

  const msg = await notificacao.obtenhaMensagemProcessada(empresa, contato, contexto)

  res.json(Resposta.sucesso(msg));
});

router.get('/grupoDeLojas/obtenha', async (req: any, res) => {
  const hostname = req.query.hostname;
  const hostname2 = hostname + ':443';

  let gruposDeLojas: any = await new MapeadorDeGrupoDeLojas().selecioneSync({
    hostname: hostname,
    hostname2: hostname2
  });

  res.json(Resposta.sucesso(gruposDeLojas));
});

router.post('/reply', async(req: any, res: any) => {
  const dados = req.body;

  console.log(dados);

  if( dados.type !== 'message' ) {
    res.json(Resposta.sucesso(true));
    return;
  }
  const empresa: Empresa = await new MapeadorDeEmpresa().selecioneCachePorDominio('fibo');

  const payload = req.body.payload;

  let nome = payload.sender.name;
  let telefone = payload.sender.phone;

  if( !nome || nome.toString() === 'undefined' || !nome.toLowerCase().match(/^[a-záàâãéèêíïóôõöúçñ ]+$/) ) {
    console.log('nome contato inválido, não salvar:' + nome)
    nome = '';
  }

  if( !telefone )  telefone = '';

  const mapeadorDeNotificacao = new MapeadorDeNotificacao();

  if( !empresa.temPedidos() ) {
    res.json({
      sucesso: true
    });

    return;
  }

  mapeadorDeNotificacao.transacao( async(conexao: any, commit: any) => {
    const notificacao: Notificacao = await mapeadorDeNotificacao.selecioneSync({
      tipoDeNotificacao: TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido
    });

    let msgPedido: any = null;

    if( notificacao ) {
      const variaveisDeRequest = new VariaveisDeRequest();

      const mapeadorDeContato = new MapeadorDeContato();

      let contato = await mapeadorDeContato.selecioneLogin({login: telefone});

      if( !contato ) {
        contato = new Contato(null, nome, telefone, null, null, null, null);

        contato.setToken(randtoken)

      }

      const sessaoLinkSaudacao = await SessaoLinkSaudacao.CrieSessao(contato, telefone, contato.codigoPais);

      await new MapeadorDeSessaoLinkSaudacao().insiraGraph(sessaoLinkSaudacao);


      const contexto = sessaoLinkSaudacao.obtenhaLinkInteligente();

      let contrato: Contrato = await new MapeadorDeContrato().selecioneSync({ idEmpresa: empresa.id})
/*
      if( contrato && contrato.qtdeOperadores > 1) {
        contexto.linkCardapio += '?op=' + usuario.id;
      }
*/
      msgPedido =  await notificacao.obtenhaMensagemProcessada(empresa, contato, contexto);

      msgPedido.fazerPreview = notificacao.fazerPreview;

      commit( () => {
        res.status(200);
        res.send(msgPedido.mensagemFinal);
      });
    } else {

    }
  });
});


router.get('/replyInsta', async(req: any, res: any) => {
  // Your verify token. Should be a random string.
  let VERIFY_TOKEN = "234";

  // Parse the query params
  let mode = req.query['hub.mode'];
  let token = req.query['hub.verify_token'];
  let challenge = req.query['hub.challenge'];

  if (mode && token) {

    // Checks the mode and token sent is correct
    if (mode === 'subscribe' && token === VERIFY_TOKEN) {

      // Responds with the challenge token from the request
      console.log('WEBHOOK_VERIFIED');
      res.status(200).send(challenge);

    } else {
      // Responds with '403 Forbidden' if verify tokens do not match
      res.sendStatus(403);
    }
  }
});

router.post('/replyInsta', async(req: any, res: any) => {
  console.log(req.body);

  res.json(Resposta.sucesso(true));
});


router.get('/qrcode', async (req, res) => {
  const codigo = req.query.c;

  new VariaveisDeRequest().obtenhaEmpresaLogada().then( (empresa) => {
    const link = empresa.obtenhaLinkLoja(Ambiente.Instance.producao);
    console.log('link loja: ' + link);

    res.setHeader('content-type', "image/png");
    QRCode.toFileStream(res, codigo, {
      width: 600,
      height: 600
    } );
  }).catch( (err) => {});
});

router.get('/pagamentos', async(req: any, res: any) => {
  const mercadopago = require('mercadopago');
  mercadopago.configurations.setAccessToken('TEST-5290199572410629-092111-0b9d16f001fab453ec27b435e565b465-34969512');

  let payment_data = {
    transaction_amount: 100,
    description: 'Título do produto',
    payment_method_id: 'pix',
    payer: {
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User'
    }
  };

  mercadopago.payment.create(payment_data).then(function (data: any) {
    const dadosResposta = data.response;

    const dadosPix = {
      id: dadosResposta.id,
      status: dadosResposta.status,
      codigo: dadosResposta.point_of_interaction.transaction_data.qr_code
    };

    res.json(Resposta.sucesso(dadosPix));
  }).catch(function (error: Error) {
    res.json(error);
  });
});

router.get('/processeAdicionais', async(req: any, res: any) => {
  const t = req.query.t;

  if( !t ) {
    res.json(Resposta.erro('Parâmetros inválidos'));
    return;
  }

  const total = Number(t);

  const mapeadorDeAdicionalTemplate = new MapeadorDeProdutoTemplateAdicional();
  mapeadorDeAdicionalTemplate.desativeMultiCliente();

  mapeadorDeAdicionalTemplate.transacao(async  (conexao: any, commit: Function) => {
    let lista = await mapeadorDeAdicionalTemplate.listeAsync({});

    let i = 0;
    for (let produtoTemplateAdicional of lista) {
      i++;

      console.log(i);
      await mapeadorDeAdicionalTemplate.atualizeSync(produtoTemplateAdicional);
    }

    const mapeador = new MapeadorDeAdicionalDeProduto();
    mapeador.desativeMultiCliente();

    lista = await mapeador.listeAsync({limite: total});

    i = 0;
    for (let adicional of lista) {
      i++;

      //console.log(adicional);
      await mapeador.atualizeSync(adicional);

      if (i % 20 === 0) {
        console.log(i);
      }
    }

    commit(() => {
      console.log('commit');
      res.json(Resposta.sucesso(lista.length));
    });
  });
});



router.get('/pedidos/guid/novo', async(req: any, res: any) => {
  res.json(Resposta.sucesso(uuid()));
});

router.get('/pedidos/horarios/indisponiveis', async(req: any, res: any) => {

  let formaDeEntrega = req.empresa.formasDeEntrega.find((forma: any) => forma.id === Number(req.query.fid));
  let dia = moment(req.query.dia, 'YYYYMMDD').format('YYYY-MM-DD 00:00:00');

  if(formaDeEntrega && formaDeEntrega.limitePedidosAgendados){
    let pedidosAgendadosDia: any = await new MapeadorDePedido().listePedidosAgendados(dia);

    let horariosAgendados = _.groupBy(pedidosAgendadosDia, (pedido: any) => moment(pedido.horarioEntregaAgendada).format('HH:mm'));

    let horariosCheios: any = [];

     Object.keys(horariosAgendados).forEach((horario: any) => {
      if(horariosAgendados[horario].length >= formaDeEntrega.limitePedidosAgendados )
        horariosCheios.push(horario)
     })

    res.json(Resposta.sucesso(horariosCheios));
  } else {
    res.json(Resposta.sucesso([]));
  }
});

router.get('/testewitai', async (req: any, res: any) => {
  const tratador = new TratadorDeMensagemWitai();

  ExecutorAsync.execute( async (callback: Function) => {
    require('domain').active.contexto.idEmpresa = -1;
    const mapeadorDeMensagem = new MapeadorDeMensagemEnviada();
    const mapeadorDeCampanha = new MapeadorDeCampanha();

    mapeadorDeCampanha.desativeMultiCliente();

    mapeadorDeCampanha.transacao( async (conexao: any, commit: any) => {
      const campanhas = await mapeadorDeCampanha.listeAsync({orderBy: true, inicio: 0, quantidade: 500000});

      let i = 0;
      console.log('campanhas: ' + campanhas.length);
      for (let campanha of campanhas) {
        i++;

        if( i % 100 === 0 ) {
          console.log(`\n\n\ncomitou ${i}\n\n\n`);
          commit();
        }
        //console.log('Consultando campanha: ' + campanha.id + ' emp: ' + campanha.empresa.id);
        require('domain').active.contexto.idEmpresa = campanha.empresa.id;
        require('domain').active.contexto.empresa = campanha.empresa;

        const mensagens: Array<any> = await mapeadorDeMensagem.listeAsync({
          idCampanha: campanha.id,
          idEmpresa: campanha.empresa.id
        });

        //console.log('mensagens: ' + mensagens.length);

        for (let mensagemEnviada of mensagens) {
          const texto = mensagemEnviada.mensagem;

          let urls: any;
          urls = texto.match(LinkEncurtado.EXPRESSAO_URL);
          let mensagemEncurtada = texto;
          let links: Array<String> = [];

          if( !urls ) {
            continue;
          }

          for (let url of urls) {
            const mapeadorDeSessaoLinkSaudacao = new MapeadorDeSessaoLinkSaudacao();

            if (url.indexOf('/link/') !== -1) {
              let hash = url.substr(url.lastIndexOf('/') + 1);

              if( hash.indexOf('?') !== -1 ) {
                //console.log('tem ? na hash: ' + hash);
                hash = hash.substr(0, hash.indexOf('?'));
                //console.log(hash);
              }

              //console.log(hash);
              const sessao = await mapeadorDeSessaoLinkSaudacao.selecioneSync({hash: hash});

              if( sessao === null ) {
                console.log(hash);
                continue;
              }

              sessao.mensagemEnviada = mensagemEnviada;
              await mapeadorDeSessaoLinkSaudacao.atualizeSync(sessao);
              //console.log('\t' + sessao.id);
            }
          }
        }
      }

      commit();

      callback();
    });
  }, () => {

  });


  res.json({
    sucesso: true
  });
});

async function obtenhaTelefonesEntidadeBitrix(metodo: string, idEntidade: number): Promise<Resposta<any>> {
  return new Promise( (resolve, reject) => {
    if( !idEntidade ) {
      resolve(Resposta.erro('Não tem id a entidade'));
      return;
    }

    const url = 'https://b24-chlbsw.bitrix24.com.br/rest/1/19i08a5m1x8am1f6/' + metodo + '.json?id=' + idEntidade;

    console.log('BITRIX: ' + url);

    axios.get(url).then((dadosContato: any) => {
      const telefones = dadosContato.data.result['PHONE'];

      resolve(Resposta.sucesso({
        entidade: dadosContato.data.result,
        telefones: telefones
      }));
    }).catch( (erro: any) => {
      resolve(Resposta.erro('Chamada não funcionou'));
    });
  });
}

router.get('/testebitrix2', async (req: any, res) => {
  console.log('\n\n----------------testebitrix');
  const empresa = req.empresa;
  const etapa = 'Teste';

  const idLead = 41467;
  let url = 'https://b24-chlbsw.bitrix24.com.br/rest/1/19i08a5m1x8am1f6/crm.lead.get.json?id=' + idLead;

  axios.get(url).then( async(dados: any) => {
    const idEmpresa = dados.data.result['COMPANY_ID'];
    const idContato = dados.data.result['CONTACT_ID'];

    console.log('BITRIX: ' + idLead + '  ' + idEmpresa + '  ' + idContato);
    const respContato: Resposta<any> = await obtenhaTelefonesEntidadeBitrix('crm.contact.get', idContato);
    const respEmpresa: Resposta<any> = await obtenhaTelefonesEntidadeBitrix('crm.company.get', idEmpresa);

    let telefones: Array<any> = [];
    let nome = '';

    if( respContato.sucesso ) {
      telefones = telefones.concat(respContato.data.telefones);
      nome = respContato.data.entidade.NAME;
    }

    if(respEmpresa.sucesso ) {
      telefones = telefones.concat(respEmpresa.data.telefones);
    }

    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    //extair codigo do pais e telefone do parametro
    let telefone = telefones[0].VALUE;
    // Adiciona + ou +55 se o telefone vier sem o + ou sem o código do país

    if (!telefone.startsWith('+')) {
      telefone = telefone.startsWith('55') ? '+' + telefone : '+55' + telefone;
    }

    const dadosTelefone = Contato.extraiCodigoPaisETelefone(telefone);

    const contato = new Contato(-1, nome, dadosTelefone.telefone + '');
    contato.codigoPais = dadosTelefone.codigoPais;


    await notificacaoService.envieNotificacaoMarketing(empresa, contato, etapa);

    res.json({
      sucesso: true,
      contato: nome,
      idEmpresa: idEmpresa,
      idContato: idContato,
      telefones: telefones
    });
  });
});

//Não apagar, é chamado pelo bitrix. Me perdoe pelo nome teste Rafael.
router.post('/testebitrix2', async (req:   any, res) => {
  console.log('BITRIX: ----------------testebitrix post');
  const empresa = req.empresa;
  console.log('BITRIX: Body parameters:');
  for (const key in req.body) {
    console.log(`BITRIX: ${key} = ${req.body[key]}`);
  }
  console.log('BITRIX: Query parameters:');
  for (const key in req.query) {
    console.log(`BITRIX: ${key} = ${req.query[key]}`);
  }
  const etapa = req.query.status;
  let usuario;

  // Search by email if email_op parameter is provided, otherwise search by name
  if (req.query.email_op) {
    // Use exact email match only
    usuario = await new MapeadorDeUsuario().selecioneSync({email: req.query.email_op,
      inicio: 0, quantidade: 1});
  } else if (req.query.op) {
    usuario = await new MapeadorDeUsuario().selecioneSync({nomeLike: req.query.op + '%',
      inicio: 0, quantidade: 1});
  }

  if (req.query.email_op) {
    console.log(`BITRIX: Buscando usuário por email: ${req.query.email_op}`);
  } else if (req.query.op) {
    console.log(`BITRIX: Buscando usuário por nome: ${req.query.op}`);
  }

  if (usuario) {
    console.log(`BITRIX: Usuário encontrado: ID=${usuario.id}, Nome=${usuario.nome}, Email=${usuario.email}`);
  } else {
    console.log('BITRIX: Nenhum usuário encontrado');
  }

  require('domain').active.contexto.usuario = usuario;

  const idLead = req.body["document_id[2]"].replace('LEAD_', '');

  let url = 'https://b24-chlbsw.bitrix24.com.br/rest/1/19i08a5m1x8am1f6/crm.lead.get.json?id=' + idLead;

  console.log(`BITRIX: Calling Bitrix API: ${url}`);

  axios.get(url).then( async(dados: any) => {
    const idEmpresa = dados.data.result['COMPANY_ID'];
    const idContato = dados.data.result['CONTACT_ID'];

    console.log(`BITRIX: IDs encontrados - Lead: ${idLead}, Empresa: ${idEmpresa}, Contato: ${idContato}`);

    const respContato: Resposta<any> = await obtenhaTelefonesEntidadeBitrix('crm.contact.get', idContato);
    const respEmpresa: Resposta<any> = await obtenhaTelefonesEntidadeBitrix('crm.company.get', idEmpresa);

    let telefones: Array<any> = [];
    let nome = '';

    if( respContato.sucesso ) {
      if( respContato.data.telefones ) {
        telefones = telefones.concat(respContato.data.telefones);
      }
      nome = respContato.data.entidade.NAME;
    }

    if(respEmpresa.sucesso ) {
      telefones = telefones.concat(respEmpresa.data.telefones);
    }

    console.log(`BITRIX: Dados do contato - Lead: ${idLead}, Nome: ${nome}`);
    console.log('BITRIX: Telefones encontrados:', JSON.stringify(telefones));

    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    let telefone = telefones[0].VALUE;

    if (!telefone.startsWith('+')) {
      telefone = telefone.startsWith('55') ? '+' + telefone : '+55' + telefone;
    }

    const dadosTelefone = Contato.extraiCodigoPaisETelefone(telefone);

    const contato = new Contato(-1, nome, dadosTelefone.telefone + '');
    contato.codigoPais = dadosTelefone.codigoPais;

//    contato.t

    await notificacaoService.envieNotificacaoMarketing(empresa, contato, etapa);

    res.json({
      sucesso: true,
      contato: nome,
      idEmpresa: idEmpresa,
      idContato: idContato,
      telefones: telefones
    });
  });
});

class Conversa {
  cliente = '';
  loja = '';
  chatId = '';
}

 function groupByMensagens(xs: any, key: string) {
  return xs.reduce(function(rv: any, x: any) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
};

router.get('/testemensagens', async (req: any, res) => {
  const mapeador = new MapeadorDeMensagemBot();

  const mensagens: Array<MensagemBot> = await mapeador.selecioneTodas({});

  const listaDeConversas = [];

  let mensagensAgrupadas: any = groupByMensagens(mensagens, 'chatId');

  for( let chatId of Object.keys(mensagensAgrupadas) ) {
    let mensagensChat = mensagensAgrupadas[chatId];

    let conversaAtual = null;
    let ultimoQueFalou = '';
    let quemFalou = '';

    for( let mensagem of mensagensChat ) {
      if( !conversaAtual ) {
        conversaAtual = new Conversa();
        conversaAtual.chatId = chatId;
      }

      if( mensagem.telefone === mensagem.chatId ) {
        conversaAtual.cliente += ' ' + mensagem.mensagem;
        quemFalou = 'cliente';
        mensagem.remetente = 'cliente';
      } else {
        conversaAtual.loja += ' ' + mensagem.mensagem;
        quemFalou = 'loja';
        mensagem.remetente = 'loja';
      }

      if( conversaAtual.loja && conversaAtual.cliente && ultimoQueFalou !== quemFalou ) {
        listaDeConversas.push(conversaAtual);
        conversaAtual = null;
      }

      ultimoQueFalou = quemFalou;
    }
  }
    /*

  *
     */

  const lista = [];

  let texto = 'prompt,completion\n';
  for( let obj of listaDeConversas ) {
    texto += `"${obj.cliente.replace(/\n/g, " ").replace(/"/g, " ").trim()}","${obj.loja.replace(/\n/g, " ").replace(/"/g, " ").trim()}"
`;
    lista.push({
      prompt: obj.cliente,
      completion: obj.cliente
    });
  }

  //res.send(texto);
  res.json(
    Resposta.sucesso(
      listaDeConversas
    )
  );
});


router.get('/testepm2', async (req: any, res) => {
  res.json(Resposta.sucesso({
    horario: new Date()
  }));
});
/*

const CryptoJS = require("crypto-js");

router.get('/testealphacode', async (req: any, res) => {
  try {
    const response = await axios.get('https://cib.alphacode.com.br/api/banners/?data=U2FsdGVkX19cghke0s7PldImw618aXQ89Gg7xe5aFCwLq9IutkY5RwbQmlueJh9k9w0Q9/wHC/2cMlxOVhk0Z1cSM29q1%2B/3kXXpDKKAFxE5/HoKcWP/hz4Uq1PU3Og6ovVTdvcTCq0hvME2TnB7johu/Ed95C%2BAw0JYQTd3BLj3j7p2ZTCN4FG5fQgpdARQHIMrkwczgW6q47XM%2B5%2BOWDna0O/OizTPKLa8TyGjRTg=');

    const senha = "m)]T5%aGZ3H9f}ufK0**K*(qTA@sO}y!UWU<o#akGks(E|XD9w3%cGUk;<c?iyF.";

    function descriptografarAES(textoCifrado: any) {
      const bytes = CryptoJS.AES.decrypt(textoCifrado, senha);
      const textoOriginal = bytes.toString(CryptoJS.enc.Utf8);
      console.log('texto: ', textoOriginal);
      return textoOriginal;
    }

    console.log(response.data);

    const data = descriptografarAES(response.data);

    console.log('vai');

    console.log('dados: ', data);
    res.json({sucesso: true});
  } catch (error) {
    // Trate os erros de acordo com suas necessidades
    console.error('Erro na chamada da API:', error);
    throw error;
  }
});
 */

router.get('/salvarChavesRedis', async (req: any, res1: any) => {
  let numProcessed = 0;
  let numDeleted = 0;
  const scanAndDelete = (cursor: string) => {
    client.scan(cursor, 'MATCH', 'sess:*', 'COUNT', 10000, (err: any, res: any) => {
      if (err) {
        console.error(err);
        return;
      }
      const [nextCursor, keys] = res;
      keys.forEach((key: string) => {
        client.ttl(key, (error: any, ttl: number) => {
          if (error) {
            console.error(err);
            return;
          }
          if (ttl < 60 * 60 * 24 * 330) { // 100 days
            client.del(key, (error2: any, deleted: number) => {
              if (error2) {
                console.error(error2);
                return;
              }
              numDeleted++;
            });
          }
          numProcessed++;
        });
      });

      console.log(`Processed ${numProcessed} keys`);
      if (nextCursor !== '0') {
        scanAndDelete(nextCursor);
      } else {
        console.log(`Deleted ${numDeleted} keys in total`);
      }
    });
  };

  scanAndDelete('0');
});

//rota que traz um prompt completo
router.get('/facaEmbed', async (req: any, res) => {
  await ProdutoEmbeddings.facaEmbedding(req.empresa);

  res.json({
    sucesso: true
  });
});


router.get('/busque2', async (req: any, res) => {
  const q = req.query.q;
  const urlOpenai = 'https://ia.openai.azure.com/openai/deployments/embedding-ada/embeddings?api-version=2023-05-15';
  const colecao = req.query.c ? req.query.c : 'produto';

  const resposta = await axios.post(urlOpenai, {
    "input": q,
    "model": "text-embedding-ada-002"
  }, {
    timeout: 12000,
    headers: {
      'Content-Type': 'application/json',
      'api-key': '********************************',
    },
  });

  const vetor = resposta.data.data[0].embedding;

  const produtosEmbeddings: Array<ProdutoEmbeddings> = await new MapeadorDeProdutoEmbeddings().listeAsync({});

  const docs = await ProdutoEmbeddings.busque(vetor, produtosEmbeddings);

  //remove a propriedade embedding dos docs
  for( let doc of docs ) {
    delete doc.doc.embedding;
  }

  res.json({
    sucesso: true,
    resultado: docs.slice(0, 10)
  })
});

let prompt = `- Você é a Mia, um bot de dúvidas da Fibonacci Soluções Ágeis. Suas respostas tem no máximo 15 palavras.
- Lista de operações disponíveis para os buttons: Visualizar Cardápio, Chave Pix, Fazer Pedido, Rastrear Pedido, Promoções do Dia, Consultar Horário de Funcionamento, Endereço da Loja, Falar com um Atendente, Métodos de Pagamento, Verificar Histórico de Pedidos, Cancelar Pedido, Falar com Atendente, Reservar Mesa, Cupons de Desconto, Plano de Fidelidade, Opções de Entrega/Retirada.
- Adicione o máximo de opções possíveis baseado no questionamento do usuário
- Pense passo a passo para responder

--user: quero fazer um pedido
--resposta: Acesse o link para fazer o pedido`;

router.get('/testeAPI', async (req: any, res) => {
  const chatGPTService = new OpenRouterGTPService();

  const msg = req.query.m;
  try {
    const resposta = await chatGPTService.chameOpenAIChat(
      '', // telefone (não necessário neste caso)
      'CHAT_GPT', // intent
      prompt,
      '', // mensagem (não necessário neste caso)
      [{
        "role": "user",
        "content": msg
      }],
      0.7, // temperatura
      '[chatgpt]');

    res.json(resposta);
  } catch (erro) {
    console.error('Erro ao chamar ChatGPT:', erro);
    res.status(500).json(Resposta.erro('Erro ao processar a solicitação'));
  }
});

router.get('/testeTypebot', async (req: any, res) => {
  const typebotService = new TypebotService();
  const idSessao = req.query.ids;
  const mensagem = req.query.m;
  const empresa = req.empresa;

  const configuracoesMia = await new MapeadorDeConfiguracoesMia().
    selecioneSync({idEmpresa: req.empresa.id});

  configuracoesMia.usarFluxoTypebot = true;
  configuracoesMia.idFluxoTypebot = 'my-typebot-ioc9dk0';
  configuracoesMia.idSessao = idSessao;
/*
  const contato = new Contato(1, 'Fulano de Tal', '62982301144');

  let resposta = null;

  if( idSessao ) {
    resposta = await typebotService.continuarFluxo(configuracoesMia, empresa,
      contato, idSessao, mensagem);
  } else {
    resposta = await typebotService.inicieFluxo(configuracoesMia, configuracoesMia.idFluxoTypebot,
      req.empresa, contato, mensagem);
  }

 */

  res.json(new CrmEmpresa());
});

router.get('/bancos', async (req: any, res: any) => {
  const username = 'rei_do_sushi';
  const url = `https://i.instagram.com/api/v1/users/web_profile_info/?username=${username}`;

  const resposta = await fetch(url, {
    headers: {
      'x-ig-app-id': '936619743392459',         // obrigatório
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      'Accept': '*/*'
    }
  });

  const data = await resposta.json();
  console.log(data.data.user);
})

// Criar lead no Bitrix24 com dados fictícios
router.get('/criar-lead-bitrix', async (req: any, res) => {
  try {
    console.log('BITRIX: Criando lead com dados fictícios...');

    // Dados fictícios para teste
    const dadosFicticios = {
      nomeResponsavel: 'Carlos Silva',
      empresa: 'Hamburgueria Gourmet Express',
      telefone: '11987654321',
      instagramHandle: 'hamburgueria_express',
      bioInsta: '🍔 Os melhores hambúrgueres artesanais da cidade! 🍟 Delivery até 23h. WhatsApp nos stories!',
      origem: 'Instagram',
      etapa: 'Prospecção',
      segmento: 'Alimentação',
      observacoes: 'Lead identificado através do Instagram. Perfil bem movimentado com stories frequentes sobre promoções. Demonstra interesse em soluções de delivery e gestão de pedidos.',
      notas: 'TEXTO ORIGINAL DO PERFIL:\n🍔 Os melhores hambúrgueres artesanais da cidade!\n🍟 Batata rústica crocante\n🥤 Bebidas geladas\n📍 Rua das Flores, 123 - Centro\n⏰ Seg-Dom: 18h às 23h\n📱 WhatsApp: (11) 98765-4321\n\nINFORMAÇÕES EXTRAÍDAS:\n- Horário: Segunda a Domingo, 18h às 23h\n- Serviços: Delivery, hambúrgueres artesanais\n- WhatsApp: (11) 98765-4321\n- Localização: Rua das Flores, 123 - Centro\n- Publicações: 847 posts'
    };

    const {
      nomeResponsavel,
      empresa,
      telefone,
      instagramHandle,
      bioInsta,
      origem,
      etapa,
      segmento,
      observacoes,
      notas
    } = dadosFicticios;

    console.log('BITRIX: Dados fictícios:', JSON.stringify(dadosFicticios, null, 2));

    // Obter ou criar empresa CRM
    let crmEmpresaIdNumeric: number;
    const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

    // Buscar empresa existente ou criar nova
    const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: empresa });

    if (existente && existente.id) {
      crmEmpresaIdNumeric = existente.id;
      console.log('BITRIX: Empresa existente encontrada. ID:', crmEmpresaIdNumeric);
    } else {
      const novaEmpresa = new CrmEmpresa(empresa);
      novaEmpresa.telefone = telefone;
      novaEmpresa.email = '<EMAIL>';
      novaEmpresa.endereco = 'Rua das Flores, 123 - Centro, São Paulo - SP';

      const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
      crmEmpresaIdNumeric = criada.id;
      console.log('BITRIX: Nova empresa criada. ID:', crmEmpresaIdNumeric);
    }

    // Criar objeto Lead
    const lead = new Lead(
      crmEmpresaIdNumeric,
      nomeResponsavel,
      empresa,
      telefone || '',
      instagramHandle,
      bioInsta,
      origem ? (OrigemLead as any)[origem] : OrigemLead.Instagram
    );

    // Aplicar dados opcionais
    lead.etapa = etapa as any;
    lead.segmento = segmento as any;
    lead.observacoes = observacoes;
    lead.notas = notas;
    lead.score = 78;

    // Dados fictícios do Instagram
    lead.instagramData = {
      bio: bioInsta,
      followers: 3247,
      following: 892,
      accountType: 'Business',
      businessCategory: 'Restaurante',
      location: 'São Paulo, Brasil',
      website: 'https://hamburgueria-express.com.br'
    };

    lead.linkInsta = 'https://hamburgueria-express.com.br';
    lead.avatarUrl = 'https://instagram.com/hamburgueria_express/profile.jpg';

    console.log('BITRIX: Lead criado:', JSON.stringify(lead, null, 2));

    // Salvar lead no banco local primeiro
    const mapeadorLead = new MapeadorDeLead();
    console.log('BITRIX: Salvando lead no banco:', JSON.stringify(lead, null, 2));

    const leadSalvo = await mapeadorLead.insiraSync(lead);

    console.log('BITRIX: Lead salvo localmente:', JSON.stringify(leadSalvo, null, 2));
    console.log('BITRIX: ID do lead salvo:', leadSalvo?.id);

    // Usar o lead salvo se tiver ID, senão usar o lead original
    const leadParaBitrix = leadSalvo?.id ? leadSalvo : lead;

    console.log('BITRIX: Usando lead para Bitrix:', JSON.stringify(leadParaBitrix, null, 2));

    // Criar lead no Bitrix
    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(leadParaBitrix);

    if (resultado.sucesso) {
      console.log('BITRIX: Lead criado no Bitrix com ID:', resultado.data);

      res.json(Resposta.sucesso({
        leadLocalId: leadSalvo?.id || null,
        bitrixId: resultado.data,
        mensagem: 'Lead criado com sucesso no sistema local e Bitrix24',
        lead: {
          id: leadSalvo?.id || null,
          nome: leadParaBitrix.nomeResponsavel,
          empresa: leadParaBitrix.empresa,
          telefone: leadParaBitrix.telefone,
          bitrixId: resultado.data
        }
      }));
    } else {
      console.error('BITRIX: Erro ao criar lead no Bitrix:', resultado.erro);

      // Lead foi salvo localmente, mas falhou no Bitrix
      res.json(Resposta.sucesso({
        leadLocalId: leadSalvo?.id || null,
        bitrixId: null,
        mensagem: `Lead criado localmente, mas falhou no Bitrix: ${resultado.erro}`,
        lead: {
          id: leadSalvo?.id || null,
          nome: leadParaBitrix.nomeResponsavel,
          empresa: leadParaBitrix.empresa,
          telefone: leadParaBitrix.telefone,
          bitrixId: null
        },
        avisoSincronizacao: true
      }));
    }

  } catch (err) {
    console.error('BITRIX: Erro ao processar criação de lead:', err);
    res.json(Resposta.erro('Erro ao criar lead: ' + err.message));
  }
});

// Sincronizar lead existente com Bitrix
router.post('/sincronizar-lead-bitrix/:id', async (req: any, res) => {
  try {
    const leadId = req.params.id;
    console.log('BITRIX: Sincronizando lead ID:', leadId);

    const mapeadorLead = new MapeadorDeLead();
    const lead = await mapeadorLead.selecioneSync({ id: leadId });

    if (!lead) {
      return res.json(Resposta.erro('Lead não encontrado'));
    }

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(lead);

    if (resultado.sucesso) {
      console.log('BITRIX: Lead sincronizado com sucesso. Bitrix ID:', resultado.data);

      res.json(Resposta.sucesso({
        leadId: leadId,
        bitrixId: resultado.data,
        mensagem: 'Lead sincronizado com Bitrix com sucesso'
      }));
    } else {
      console.error('BITRIX: Erro ao sincronizar lead:', resultado.erro);
      res.json(Resposta.erro(`Erro ao sincronizar com Bitrix: ${resultado.erro}`));
    }

  } catch (err) {
    console.error('BITRIX: Erro ao sincronizar lead:', err);
    res.json(Resposta.erro('Erro ao sincronizar lead: ' + err.message));
  }
});

// Testar integração com Bitrix
router.get('/teste-bitrix', async (req: any, res) => {
  try {
    console.log('BITRIX: Executando teste de integração...');

    // Criar lead de teste
    const leadTeste = new Lead(
      1, // crmEmpresaId padrão
      'Teste Bitrix',
      'Empresa Teste Bitrix Ltda',
      '***********',
      'teste_bitrix_api',
      'Bio de teste para validação da integração com Bitrix24',
      OrigemLead.Instagram
    );

    leadTeste.score = 90;
    leadTeste.segmento = 'Restaurante' as any;
    leadTeste.observacoes = 'Lead de teste criado via API para validar integração com Bitrix24';
    leadTeste.instagramData = {
      followers: 2500,
      following: 180,
      accountType: 'Business',
      businessCategory: 'Restaurante',
      bio: 'Restaurante especializado em culinária brasileira'
    };

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(leadTeste);

    if (resultado.sucesso) {
      console.log('BITRIX: Teste realizado com sucesso. Bitrix ID:', resultado.data);

      res.json(Resposta.sucesso({
        bitrixId: resultado.data,
        mensagem: 'Teste de integração Bitrix24 realizado com sucesso',
        leadTeste: {
          nome: leadTeste.nomeResponsavel,
          empresa: leadTeste.empresa,
          telefone: leadTeste.telefone,
          instagram: leadTeste.instagramHandle
        },
        timestamp: new Date().toISOString()
      }));
    } else {
      console.error('BITRIX: Falha no teste:', resultado.erro);
      res.json(Resposta.erro(`Falha no teste de integração: ${resultado.erro}`));
    }

  } catch (err) {
    console.error('BITRIX: Erro no teste de integração:', err);
    res.json(Resposta.erro('Erro no teste de integração: ' + err.message));
  }
});

// Listar custom fields do Bitrix
router.get('/bitrix-custom-fields', async (req: any, res) => {
  try {
    console.log('BITRIX: Listando custom fields de leads...');

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.listarCustomFields();

    if (resultado.sucesso) {
      console.log('BITRIX: Custom fields listados com sucesso. Total:', resultado.data.length);

      res.json(Resposta.sucesso({
        total: resultado.data.length,
        customFields: resultado.data,
        mensagem: 'Custom fields listados com sucesso'
      }));
    } else {
      console.error('BITRIX: Falha ao listar custom fields:', resultado.erro);
      res.json(Resposta.erro(`Falha ao listar custom fields: ${resultado.erro}`));
    }

  } catch (err) {
    console.error('BITRIX: Erro ao listar custom fields:', err);
    res.json(Resposta.erro('Erro ao listar custom fields: ' + err.message));
  }
});

// Buscar custom field específico do Bitrix por ID
router.get('/bitrix-custom-fields/:id', async (req: any, res) => {
  try {
    const fieldId = req.params.id;

    if (!fieldId) {
      return res.json(Resposta.erro('ID do custom field é obrigatório'));
    }

    console.log('BITRIX: Buscando custom field ID:', fieldId);

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.buscarCustomField(fieldId);

    if (resultado.sucesso) {
      console.log('BITRIX: Custom field encontrado:', resultado.data.fieldName);

      res.json(Resposta.sucesso({
        customField: resultado.data,
        mensagem: `Custom field ${fieldId} encontrado com sucesso`
      }));
    } else {
      console.error('BITRIX: Falha ao buscar custom field:', resultado.erro);
      res.json(Resposta.erro(`Falha ao buscar custom field: ${resultado.erro}`));
    }

  } catch (err) {
    console.error('BITRIX: Erro ao buscar custom field:', err);
    res.json(Resposta.erro('Erro ao buscar custom field: ' + err.message));
  }
});

// Listar custom fields completos do Bitrix (lista + detalhes para cada campo)
router.get('/bitrix-custom-fields-completos', async (req: any, res) => {
  try {
    let t: LeadLink;
    let s = new CrmEmpresa();
    let lead = new Lead(1, 'Teste', 'Teste', '***********', 'teste', 'teste', OrigemLead.Instagram);

    t = new LeadLink(1, TipoLinkLead.Ifood, 'https://ifood.com.br/teste',
      'https://ifood.com.br/teste',  1);

    lead.adicionarLink(TipoLinkLead.Ifood,
      'https://ifood.com.br/teste', 'https://ifood.com.br/teste');

    if( true ) {
      res.json(Resposta.sucesso(lead));
      return;
    }

    console.log('BITRIX: Listando custom fields completos...');

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.listarCustomFieldsCompletos();

    if (resultado.sucesso) {
      console.log('BITRIX: Custom fields completos listados com sucesso. Total:', resultado.data.length);

      res.json(Resposta.sucesso({
        total: resultado.data.length,
        customFields: resultado.data,
        mensagem: 'Custom fields completos listados com sucesso'
      }));
    } else {
      console.error('BITRIX: Falha ao listar custom fields completos:', resultado.erro);
      res.json(Resposta.erro(`Falha ao listar custom fields completos: ${resultado.erro}`));
    }

  } catch (err) {
    console.error('BITRIX: Erro ao listar custom fields completos:', err);
    res.json(Resposta.erro('Erro ao listar custom fields completos: ' + err.message));
  }
});

export const ApiController: Router = router;

