import {Router} from 'express';
import {NotificacaoService} from '../service/NotificacaoService';
import {EnviadorDeMensagemSMSDev} from '../service/EnviadorDeMensagemSMSDev';
import {SituacaoDeMensagem} from '../service/SituacaoDeMensagem';
import {MapeadorDeNotificacao} from '../mapeadores/MapeadorDeNotificacao';
import {Resposta} from "../utils/Resposta";
import {Notificacao} from "../domain/Notificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {VariaveisDeRequest} from "../service/VariaveisDeRequest";
import {Ambiente} from "../service/Ambiente";
import {Empresa} from "../domain/Empresa";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {ConfigWhatsappService} from "../service/ConfigWhatsappService";
import {MapeadorPerformanceWhatsapp} from "../mapeadores/MapeadorPerformanceWhatsapp";
import {ObjetoRelatorio} from "../domain/relatorio/ObjetoRelatorio";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import * as moment from "moment";
import {PhoneNumberType, PhoneNumberUtil} from 'google-libphonenumber';
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {StatusDeMensagem} from "../service/StatusDeMensagem";
import {Contato} from "../domain/Contato";
import {CacheService} from "../service/CacheService";
import {Token} from "../domain/Token";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";

const randtoken = require('rand-token');

const router: Router = Router();

router.get('/', async (req: any, res: any) => {
  const mapeadorDeNotificacao = new MapeadorDeNotificacao();

  let notificacoes: Array<Notificacao> = await mapeadorDeNotificacao.listeAsync({ });

  for(let notificacao of notificacoes ) {
    //chama uma função da notificação para converter o json menu em objeto
    notificacao.ajusteMenu();
  }

  if(!req.empresa.meucardapioPay || !req.empresa.meucardapioPay.lojaAtivou)
    notificacoes = notificacoes.filter((item: any) => !item.pagamentosOnlineMeucadapioPay())

  res.json({
    sucesso: true,
    data: notificacoes
  });
});

router.get('/mensagem/processe', async (req, res) => {
  let tipo = req.query.tipo,
    idContato = Number(req.query.cid);

  if(!tipo || !(idContato > 0))
    return res.json(Resposta.sucesso(''))

  new MapeadorDeContato().selecioneSync(idContato).then( contato => {
    new MapeadorDeNotificacao().selecioneSync({tipoDeNotificacao: tipo}).then( notificacao => {
      new VariaveisDeRequest().obtenhaEmpresaLogada().then( empresa => {
        notificacao.obtenhaMensagemProcessada(empresa, contato).then(  (resposta: any) => {
          res.json(Resposta.sucesso(resposta))
        }).catch( (erro: any) => {
          res.json(Resposta.erro(erro))
        })
      })
    })
  })
});

function verificaLinksPermitidos(urlDominio: string, mensagem: string): boolean {
  // Regex para identificar todos os links na mensagem
  const regexLinks = /\bhttps?:\/\/\S+/g;

  // Encontrar todos os links na mensagem
  const linksEncontrados = mensagem.match(regexLinks);

  // Verificar se existem links na mensagem
  if (linksEncontrados) {
    // Verificar cada link encontrado
    for (const link of linksEncontrados) {
      if (!link.includes(urlDominio) && !link.includes('meucardapio.ai')) {
        return false; // Link não permitido encontrado
      }
    }
  }

  // Se todos os links são permitidos ou não existem links
  return true;
}
router.put('/', RotaGuard.alterarCadastrarNotificacoes, async (req: any, res: any) => {
  const dados = req.body;
  const empresa: Empresa = req.empresa;

  const notificacao = new Notificacao();
  if( dados.temMenu ) {
    notificacao.menu = dados.menu;
  }

  Object.assign(notificacao, dados);

  if( notificacao.tipoDeNotificacao === TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido ) {
    const mensagemEhValida = verificaLinksPermitidos(empresa?.urlDaEmpresa?.hostname, notificacao.mensagem);

    if (!mensagemEhValida) {
      return res.json(Resposta.erro('Não é permitido enviar links de outros sites na mensagem.'));
    }
  }

  new NotificacaoService(null).atualize(notificacao).then(async (resp) => {
    await new ConfigWhatsappService().atualize(empresa, dados.tempoMensagemWhatsapp);
    console.log(resp);

    res.json(Resposta.sucesso());
  }).catch( (erro: any) => {
    console.log(erro);
    res.json(Resposta.erro(erro));
  });
});


router.get('/acompanhe/:id', (req, res) => {
  const idSituacao = req.params.id;
  const servicoDeEnvios = new NotificacaoService(new EnviadorDeMensagemSMSDev());

  servicoDeEnvios.verifiqueStatus(idSituacao).then((situacao: SituacaoDeMensagem) => {
    res.json(situacao);
  });
});


router.post('/extrato/envie', async (req: any, res: any) => {
  let dados = req.body;
  let empresa = req.empresa;

  if(dados.id){
    let contato = await new MapeadorDeContato().selecioneSync(Number(dados.id));

    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    notificacaoService.envieLinkExtrato(contato).then( (resposta: Resposta<any>) => {
      res.json(Resposta.sucesso())
    });


  }
})

router.get('/performance-whatsapp', async(req: any, res: any) => {
  const usuario = req.user;
  const di = req.query.di;
  const df = req.query.df;

  const dataInicio = moment(di, "YYYY-MM-DD").format('YYYY-MM-DD');
  const dataFim = moment(df, "YYYY-MM-DD").add(1, 'd').format('YYYY-MM-DD');
  const mapeador = new MapeadorPerformanceWhatsapp();

  console.log(dataInicio);
  console.log(dataFim);
  const dados: Array<ObjetoRelatorio> = await mapeador.listePerformance({
    rede: usuario.adminRede,
    dataInicio: dataInicio,
    dataFim: dataFim
  });

  res.json(Resposta.sucesso(dados));
});

function extrairCodigoPais(numero: string): string | null {
  try {
    const phoneUtil = PhoneNumberUtil.getInstance();
    const numeroObj = phoneUtil.parse(numero);
    if (phoneUtil.isValidNumber(numeroObj)) {
      return "+" + numeroObj.getCountryCode();
    } else {
      console.error('Número de telefone inválido:', numero);
      return null;
    }
  } catch (e) {
    console.error('Número de telefone inválido:', e);
    return null;
  }
}

router.get('/:tipo', async(req: any, res: any) => {
  const tipo = req.params.tipo;

  //checa se o param tipo veio
  if(!tipo) {
    return res.json(Resposta.erro('É necessário informar um tipo de notificação'));
  }

  const mapeadorDeNotificacao = new MapeadorDeNotificacao();

  const notificacao = await mapeadorDeNotificacao.selecioneSync({
    tipoDeNotificacao: tipo
  });

  res.json(Resposta.sucesso(notificacao));
});

router.post('/mensagem/respostaMensagemLista', async(req: any, res: any) => {
  const empresa = req.empresa;
  //pega o ido do body ou do query do req
  const ido =  req.body.ido || req.query.ido;

  if( !ido ) {
    res.json({
      sucesso: false,
      mensagem: 'É necessário informar o id da mensagem'
    });
    return;
  }

  const idMensagem = ido.split('-')[0];
  const textoOpcao = ido.split('-')[1];

  const mensagem: MensagemEnviada = await new MapeadorDeMensagemEnviada().selecioneSync({id: idMensagem});
  mensagem.processeMenu();

  const resposta = mensagem.obtenhaResposta(textoOpcao);

  const notificacao: Notificacao = new Notificacao();
  notificacao.mensagem = resposta;

  notificacao.temMenu = false;

  const contexto = {};

  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

  await notificacaoService.crieLinkCardapio(mensagem.contato, contexto, mensagem.tipoDeNotificacao === TipoDeNotificacaoEnum.TesteCampanha
    || mensagem.tipoDeNotificacao.indexOf('Etapa') !== -1);

  const mensagemFinal = await notificacao.obtenhaMensagemProcessada(mensagem.empresa, mensagem.contato, contexto, true);

  res.json(Resposta.sucesso(mensagemFinal.mensagemFinal));
});

router.get('/mensagem/notificacaoCodigoValidacao', async(req: any, res: any) => {
  let empresa: Empresa = req.empresa;
  let idContato = req.query.id
  let idMensagemPendente = req.query.msId

  if(idContato){
    let contato = await new MapeadorDeContato().selecioneSync({id: idContato})

    if(contato){
      const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

      let codigo = Token.gereCodigo();

      let msgValidar: any =   notificacaoService.obtenhaMesnsagemValidarTelefone(empresa, contato, codigo);

      const mensagem = MensagemEnviada.nova(contato, empresa.numeroWhatsapp, msgValidar.mensagemFinal,
        TipoDeNotificacaoEnum.CodigoConfirmacao, StatusDeMensagem.Nova, null);

      mensagem.telefone = contato.codigoPais + contato.telefone;

      await new MapeadorDeMensagemEnviada().insiraSync(mensagem);

      CacheService.insiraCodigoValidacao(contato, codigo, mensagem);

      if(idMensagemPendente)
        await new MapeadorDeMensagemEnviada().atualizeStatus({id: Number(idMensagemPendente), status: StatusDeMensagem.Cancelada});

      let resposta: any  = {
        sucesso: true,
        data: {
          msg: msgValidar,
          marcarComoLida: true
        }
      }
      res.json(resposta);
    } else {
      res.json(Resposta.erro("Contato inválido"))
    }
  } else {
    res.json(Resposta.erro("É necessário informar id contato"))
  }
});


router.get('/mensagem/notificacao/linkPagamento/:guid', async(req: any, res: any) => {
  let empresa: Empresa = req.empresa;
  let pedido  = await new MapeadorDePedido().selecioneSync({guid: req.params.guid});

  if(pedido){
    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

    let resposta: any = await notificacaoService.obtenhaMensagemLinkPagamentoPix(empresa, pedido, req.user);

    if(resposta){
      const mensagem = MensagemEnviada.nova({empresa: empresa} as Contato, empresa.numeroWhatsapp, resposta.data.msg.mensagemFinal,
        TipoDeNotificacaoEnum.LinkPagamentoPedido, null);

       const telefone: string =  (pedido.contato.codigoPais + pedido.contato.telefone);
       await setMensagemResposta(mensagem, resposta, telefone, req.user );

       resposta.data.telefone = telefone
       res.json(resposta);
    }
  }

});

async function setMensagemResposta(mensagem: MensagemEnviada, resposta: any, telefone: string, usuario: any) {
  if(resposta.data.msg.temMenu){
    mensagem.contato = resposta.contato;
    mensagem.telefone = telefone;
    mensagem.status = StatusDeMensagem.Enviada;
    mensagem.temMenu = resposta.data.msg.temMenu;
    mensagem.menu = JSON.stringify(resposta.data.msg.menu);
    mensagem.tipoDeNotificacao = TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido;

    await new MapeadorDeMensagemEnviada().insiraSync(mensagem);

    mensagem.processeMenu();
    resposta.data.msg.assinar = usuario.assinarMensagens;

    resposta.data.msg.assinatura = usuario.nome;
    resposta.data.msg.menu = mensagem.menu;
  }

  mensagem.enviarLinksBotao = resposta.data.msg.enviarLinksBotao;

  await mensagem.processeLinksParaPreview();
  resposta.data.msg.id = mensagem.id;

  resposta.data.msg.linkPreview = mensagem.linkPreview;
}


router.get('/mensagem/notificacaoSaudade', async(req: any, res: any) => {
  let empresa: Empresa = req.empresa;
  let nome = req.query.nome;
  let telefone = decodeURIComponent(req.query.telefone);
  const usuario = req.user;

  console.log('telefone: ' + telefone);
  let telefoneLibGoogle = PhoneNumberUtil.getInstance().parse(telefone);
  let tipo = PhoneNumberUtil.getInstance().getNumberType(telefoneLibGoogle);

  if( tipo !== PhoneNumberType.FIXED_LINE && telefone.length  < 14 && telefone.startsWith('+55')) {
    telefone = telefone.substr(0, 5) + '9' + telefone.substr(5);
  }

  if( !nome || nome.toString() === 'undefined' || !nome.toLowerCase().match(/^[a-záàâãéèêíïóôõöúçñ ]+$/) ) {
    console.log('nome contato inválido, não salvar:' + nome)
    nome = '';
  }

  if( !telefone )
   return res.json(Resposta.erro("É necessário informar um telefone válido " + telefone))


  if(!telefone.startsWith('+'))
    return res.json(Resposta.erro("É necessário informar um telefone válido " + telefone))

  let codigoPais = extrairCodigoPais(telefone)


  if(!codigoPais)
    return res.json(Resposta.erro("É necessário informar um telefone válido " + telefone))

  telefone = telefone.substring(codigoPais.length)

  if( !empresa.temPedidos() ) {

    res.json({
      sucesso: true
    });
    return;

  }

  const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(empresa));

  notificacaoService.obtenhaMensagemSaudacao(empresa, nome, codigoPais, telefone, usuario).then( async (resposta) => {
    const mensagem = MensagemEnviada.nova({empresa: empresa} as Contato, empresa.numeroWhatsapp, resposta.data.msg.mensagemFinal,
    TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido, null);

    await setMensagemResposta(mensagem, resposta, (codigoPais + telefone),  usuario);

    res.json(resposta);
  });
});

router.get('/emperigo/execute/:qtdeNotificar', async(req: any, res: any) => {
  let notificacao =
    await  new MapeadorDeNotificacao().selecioneSync( { tipoDeNotificacao: TipoDeNotificacaoEnum.SentimosSuaFalta, ativa: true })

  if(!notificacao) return res.json(Resposta.erro('Nenhuma notificação sentimos sua falta ativa'))

  let qtdeNotificar = Number(req.params.qtdeNotificar);
  ExecutorAsync.execute(   (cb: Function) => {
    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(notificacao.empresa));
    let contexto = require('domain').active.contexto;
    contexto.idEmpresa = notificacao.empresa.id;

    notificacaoService.envieNotificacaoClientesEmPerigo(notificacao, qtdeNotificar ).then( () => {
      console.log('fim execução notificaçoes em perigo')
      cb();
    });
  }, 0);

  res.json(Resposta.sucesso(String(`Execução notificaçoes saudades disparda para no máximo ${qtdeNotificar} contatos`)));
});

export const NotificacaoController: Router = router;
