import axios from 'axios';
import {Lead} from '../../domain/crm/Lead';
import { Socio } from '../../domain/crm/CrmEmpresa';
import { Resposta } from '../../utils/Resposta';

interface BitrixConfig {
  baseUrl: string;
  userId: string;
  webhook: string;
}

interface BitrixPhone {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'MOBILE' | 'HOME' | 'OTHER';
}

interface BitrixEmail {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixWeb {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixContactFields {
  NAME?: string;
  LAST_NAME?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixContactPayload {
  fields: BitrixContactFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

interface BitrixLeadFields {
  TITLE?: string;
  NAME?: string;
  LAST_NAME?: string;
  COMPANY_TITLE?: string;
  STATUS_ID?: string;
  SOURCE_ID?: string;
  SOURCE_DESCRIPTION?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  WEB?: BitrixWeb[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  CONTACT_ID?: number; // Vinculação com contato
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixLeadPayload {
  fields: BitrixLeadFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

interface CustomFieldMapping {
  label: string;
  fieldName: string;
  leadProperty: string;
  transform?: (value: any) => any;
}

export class BitrixService {
  private config: BitrixConfig;
  private customFieldsCache: Map<string, any> = new Map();
  private customFieldsMappingCache: Map<string, string> = new Map();

  constructor(config: BitrixConfig) {
    this.config = config;
  }

  /**
   * Valida estrutura do payload antes de enviar para o Bitrix
   */
  private validarPayload(payload: any): void {
    const issues: string[] = [];

    console.log('VALIDAÇÃO: Verificando estrutura do payload...');

    if (!payload.fields) {
      issues.push('Campo "fields" obrigatório está ausente');
    } else {
      const fields = payload.fields;

      // Verificar campos obrigatórios
      if (!fields.TITLE) {
        issues.push('Campo TITLE obrigatório está ausente');
      }

      if (!fields.NAME && !fields.LAST_NAME) {
        issues.push('Pelo menos um dos campos NAME ou LAST_NAME deve estar presente');
      }

      // Verificar formato dos arrays de contato
      if (fields.PHONE && !Array.isArray(fields.PHONE)) {
        issues.push('Campo PHONE deve ser um array');
      }

      if (fields.EMAIL && !Array.isArray(fields.EMAIL)) {
        issues.push('Campo EMAIL deve ser um array');
      }

      if (fields.WEB && !Array.isArray(fields.WEB)) {
        issues.push('Campo WEB deve ser um array');
      }

      // Verificar estrutura dos telefones
      if (fields.PHONE && Array.isArray(fields.PHONE)) {
        fields.PHONE.forEach((phone: any, index: number) => {
          if (!phone.VALUE) {
            issues.push(`PHONE[${index}]: Campo VALUE é obrigatório`);
          }
          if (!phone.VALUE_TYPE || !['WORK', 'MOBILE', 'HOME', 'OTHER'].includes(phone.VALUE_TYPE)) {
            issues.push(`PHONE[${index}]: Campo VALUE_TYPE deve ser WORK, MOBILE, HOME ou OTHER`);
          }
        });
      }

      // Verificar estrutura dos emails
      if (fields.EMAIL && Array.isArray(fields.EMAIL)) {
        fields.EMAIL.forEach((email: any, index: number) => {
          if (!email.VALUE) {
            issues.push(`EMAIL[${index}]: Campo VALUE é obrigatório`);
          }
          if (!email.VALUE_TYPE || !['WORK', 'HOME', 'OTHER'].includes(email.VALUE_TYPE)) {
            issues.push(`EMAIL[${index}]: Campo VALUE_TYPE deve ser WORK, HOME ou OTHER`);
          }
        });
      }

      // Verificar campos customizados
      const customFields = Object.keys(fields).filter(key => key.startsWith('UF_'));
      console.log(`VALIDAÇÃO: Encontrados ${customFields.length} campos customizados:`, customFields);

      // Verificar tamanhos dos campos
      if (fields.TITLE && fields.TITLE.length > 255) {
        issues.push('Campo TITLE excede 255 caracteres');
      }

      if (fields.COMMENTS && fields.COMMENTS.length > 1000) {
        issues.push('Campo COMMENTS excede 1000 caracteres');
      }
    }

    if (issues.length > 0) {
      console.error('VALIDAÇÃO: Problemas encontrados no payload:');
      issues.forEach((issue, index) => {
        console.error(`  ${index + 1}. ${issue}`);
      });
      console.warn('VALIDAÇÃO: Payload pode causar erro no Bitrix');
    } else {
      console.log('VALIDAÇÃO: ✓ Payload está válido');
    }
  }

  /**
   * Configuração de mapeamento dos custom fields baseado no JSON real do Bitrix
   */
  private readonly CUSTOM_FIELD_MAPPINGS: CustomFieldMapping[] = [
    {
      label: "Instagram",
      fieldName: "UF_CRM_1615222177542",
      leadProperty: "getLinkInstagram", // Método que busca link do Instagram
      transform: (value) => value || ''
    },
    {
      label: "Telefone de contato ",
      fieldName: "UF_CRM_1621521259444",
      leadProperty: "telefone",
      transform: (value) => {
        if (!value) return '';
        // Return only digits for the custom phone field (Bitrix expects numbers only)
        const numeroLimpo = value.replace(/\D/g, '');
        return numeroLimpo;
      }
    },
    {
      label: "Site",
      fieldName: "UF_CRM_1621947262974",
      leadProperty: "getLinkSite", // Método que busca link do site
      transform: (value) => value || ''
    },
    {
      label: "CNPJ",
      fieldName: "UF_CRM_1621948447906",
      leadProperty: "cnpj"
    },
    {
      label: "Rapport",
      fieldName: "UF_CRM_1622222037",
      leadProperty: "observacoes"
    },
    {
      label: "Link Ifood",
      fieldName: "UF_CRM_1623334200",
      leadProperty: "getLinkIfood", // Método que busca link do iFood
      transform: (value) => value || ''
    },
    {
      label: "Link Uber Eats",
      fieldName: "UF_CRM_1623334228",
      leadProperty: "linkUberEats"
    },
    {
      label: "Link Rappi",
      fieldName: "UF_CRM_1623431063",
      leadProperty: "linkRappi"
    },
    {
      label: "Link 99Food",
      fieldName: "UF_CRM_1634307256707",
      leadProperty: "link99Food"
    },
    {
      label: "Link do ifood ",
      fieldName: "UF_CRM_1650463024814",
      leadProperty: "getLinkIfood", // Duplicado - usar o mesmo método
      transform: (value) => value || ''
    },
    {
      label: "Site do Concorrente",
      fieldName: "UF_CRM_1623263814",
      leadProperty: "getLinkConcorrente", // Método que busca link de concorrente
      transform: (value) => value || ''
    },
    {
      label: "Todos os Links",
      fieldName: "UF_CRM_1749901110452",
      leadProperty: "getAllLinksUrls", // Método que retorna array de URLs
      transform: (value) => Array.isArray(value) ? value : []
    }
  ];

  /**
   * Cria um contato no Bitrix24
   */
  async criarContato(lead: Lead): Promise<Resposta<number>> {
    try {
      const payload = this.converterLeadParaContato(lead);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.add.json`;

      console.log('BITRIX: Criando contato:', JSON.stringify(payload, null, 2));
      console.log('BITRIX: URL:', url);

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro ao criar contato:', response.data);
        return Resposta.erro(`Erro do Bitrix ao criar contato: ${response.data.error_description}`) as any;
      }

      const contactId = parseInt(response.data.result);
      console.log('BITRIX: Contato criado com sucesso. ID:', contactId);

      return Resposta.sucesso(contactId) as Resposta<number>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição de contato:', error);
      return Resposta.erro(`Erro ao conectar com Bitrix para criar contato: ${error.message}`) as any;
    }
  }

  /**
   * Cria múltiplos contatos no Bitrix24 para todos os sócios de um lead
   */
  async criarMultiplosContatos(lead: Lead): Promise<Resposta<{ contatosPrincipais: number[], contatosSecundarios: number[], erros: string[] }>> {
    const timestamp = new Date().toISOString();
    console.log(`\n====== BITRIX MULTIPLE CONTACTS CREATION START [${timestamp}] ======`);

    const socios = lead.getSocios();
    const contatosPrincipais: number[] = [];
    const contatosSecundarios: number[] = [];
    const erros: string[] = [];

    console.log('BITRIX: Criando contatos para', socios.length, 'sócios...');

    if (socios.length === 0) {
      console.log('BITRIX: Nenhum sócio encontrado, usando contato principal do lead');
      const resultadoContato = await this.criarContato(lead);

      if (resultadoContato.sucesso) {
        contatosPrincipais.push(resultadoContato.data);
      } else {
        erros.push(`Erro ao criar contato principal: ${resultadoContato.erro}`);
      }
    } else {
      // Criar contatos para todos os sócios
      for (let i = 0; i < socios.length; i++) {
        const socio = socios[i];
        console.log(`BITRIX: Criando contato ${i + 1}/${socios.length} para sócio: ${socio.nome} (Principal: ${socio.principal})`);

        try {
          const resultadoContato = await this.criarContatoParaSocio(lead, socio);

          if (resultadoContato.sucesso) {
            if (socio.principal) {
              contatosPrincipais.push(resultadoContato.data);
              console.log(`BITRIX: ✓ Contato principal criado para ${socio.nome}, ID:`, resultadoContato.data);
            } else {
              contatosSecundarios.push(resultadoContato.data);
              console.log(`BITRIX: ✓ Contato secundário criado para ${socio.nome}, ID:`, resultadoContato.data);
            }
          } else {
            const mensagemErro = `Erro ao criar contato para ${socio.nome}: ${resultadoContato.erro}`;
            erros.push(mensagemErro);
            console.error('BITRIX: ✗', mensagemErro);
          }

          // Pequena pausa entre criações para evitar rate limiting
          if (i < socios.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }

        } catch (error) {
          const mensagemErro = `Erro inesperado ao criar contato para ${socio.nome}: ${error.message}`;
          erros.push(mensagemErro);
          console.error('BITRIX: ✗', mensagemErro);
        }
      }
    }

    const resultado = {
      contatosPrincipais,
      contatosSecundarios,
      erros
    };

    console.log(`\nBITRIX: === RESUMO DA CRIAÇÃO DE CONTATOS ===`);
    console.log(`✓ Contatos principais criados: ${contatosPrincipais.length}`);
    console.log(`✓ Contatos secundários criados: ${contatosSecundarios.length}`);
    console.log(`✗ Erros: ${erros.length}`);

    if (erros.length > 0) {
      console.log('Erros detalhados:', erros);
    }

    console.log(`====== BITRIX MULTIPLE CONTACTS CREATION END [${new Date().toISOString()}] ======\n`);

    return Resposta.sucesso(resultado) as Resposta<{ contatosPrincipais: number[], contatosSecundarios: number[], erros: string[] }>;
  }

  /**
   * Cria um contato específico para um sócio
   */
  private async criarContatoParaSocio(lead: Lead, socio: Socio): Promise<Resposta<number>> {
    try {
      const payload = this.converterSocioParaContato(lead, socio);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.add.json`;

      console.log(`BITRIX: Criando contato para sócio ${socio.nome}:`, JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error(`BITRIX: Erro ao criar contato para ${socio.nome}:`, response.data);
        return Resposta.erro(`Erro do Bitrix ao criar contato para ${socio.nome}: ${response.data.error_description}`) as any;
      }

      const contactId = parseInt(response.data.result);
      console.log(`BITRIX: Contato criado para ${socio.nome} com sucesso. ID:`, contactId);

      return Resposta.sucesso(contactId) as Resposta<number>;
    } catch (error) {
      console.error(`BITRIX: Erro na requisição de contato para ${socio.nome}:`, error);
      return Resposta.erro(`Erro ao conectar com Bitrix para criar contato para ${socio.nome}: ${error.message}`) as any;
    }
  }

  /**
   * Cria um lead no Bitrix24 (agora com criação de contato primeiro)
   */
  async criarLead(lead: Lead): Promise<Resposta<number>> {
    const timestamp = new Date().toISOString();
    console.log(`\n====== BITRIX LEAD CREATION START [${timestamp}] ======`);

    // DEBUG: Verificação inicial do parâmetro
    console.log('BITRIX criarLead DEBUG: Tipo do parâmetro:', typeof lead);
    console.log('BITRIX criarLead DEBUG: Constructor name:', lead?.constructor?.name);
    console.log('BITRIX criarLead DEBUG: É objeto?', lead && typeof lead === 'object');
    console.log('BITRIX criarLead DEBUG: Tem propriedade hasSocios?', 'hasSocios' in lead);
    console.log('BITRIX criarLead DEBUG: Tipo de hasSocios:', typeof lead?.hasSocios);


    console.log('BITRIX: Lead object received:', {
      id: lead.id,
      empresa: lead.empresa,
      nomeResponsavel: lead.nomeResponsavel,
      telefone: lead.telefone,
      email: lead.crmEmpresa?.email,
      instagramHandle: lead.instagramHandle,
      etapa: lead.etapa,
      origem: lead.origem,
      segmento: lead.segmento,
      cnpj: lead.cnpj,
      linksCount: lead.links?.length || 0
    });

    try {
      let contactId: number | undefined;
      let contatosSecundarios: number[] = [];

      // Primeira etapa: Criar contatos para todos os sócios (se houver) ou contato principal
      console.log('BITRIX: === CRIANDO CONTATOS ===');

      // DEBUG: Verificar objeto lead
      console.log('BITRIX DEBUG: Tipo de lead:', typeof lead);
      console.log('BITRIX DEBUG: Constructor name:', lead?.constructor?.name);
      console.log('BITRIX DEBUG: Propriedades do lead:', Object.keys(lead));
      console.log('BITRIX DEBUG: Tem método hasSocios?', typeof lead.hasSocios === 'function');
      console.log('BITRIX DEBUG: Tem crmEmpresa?', !!lead.crmEmpresa);

      // Verificação segura
      let temSocios = false;
      try {
        if (typeof lead.hasSocios === 'function') {
          temSocios = lead.hasSocios();
          console.log('BITRIX DEBUG: hasSocios() retornou:', temSocios);
        } else {
          console.log('BITRIX DEBUG: lead.hasSocios não é uma função!');
          console.log('BITRIX DEBUG: Métodos disponíveis:', Object.getOwnPropertyNames(Object.getPrototypeOf(lead)));
        }
      } catch (erro) {
        console.error('BITRIX DEBUG: Erro ao chamar hasSocios():', erro);
      }

      if (temSocios) {
        console.log('BITRIX: Lead possui', lead.getQuantidadeSocios(), 'sócios. Criando múltiplos contatos...');

        const resultadoContatos = await this.criarMultiplosContatos(lead);

        if (resultadoContatos.sucesso) {
          const dados = resultadoContatos.data;

          // Usar o primeiro contato principal como contato do lead
          if (dados.contatosPrincipais.length > 0) {
            contactId = dados.contatosPrincipais[0];
            console.log('BITRIX: ✓ Contato principal selecionado para o lead, ID:', contactId);
          }

          // Armazenar contatos secundários para potencial uso futuro
          contatosSecundarios = [...dados.contatosPrincipais.slice(1), ...dados.contatosSecundarios];

          if (contatosSecundarios.length > 0) {
            console.log('BITRIX: ✓', contatosSecundarios.length, 'contatos secundários criados:', contatosSecundarios);
          }

          if (dados.erros.length > 0) {
            console.warn('BITRIX: ⚠ Alguns contatos falharam, mas prosseguindo com criação do lead');
            dados.erros.forEach(erro => console.warn('BITRIX: ✗', erro));
          }
        } else {
          console.warn('BITRIX: ✗ Falha ao criar múltiplos contatos:', resultadoContatos.erro);
          console.warn('BITRIX: Tentando fallback para contato principal...');

          // Fallback: tentar criar contato principal tradicional
          if (lead.nomeResponsavel && lead.nomeResponsavel.trim()) {
            const resultadoContato = await this.criarContato(lead);

            if (resultadoContato.sucesso) {
              contactId = resultadoContato.data;
              console.log('BITRIX: ✓ Contato fallback criado com sucesso, ID:', contactId);
            }
          }
        }
      } else if (lead.nomeResponsavel && lead.nomeResponsavel.trim()) {
        console.log('BITRIX: Nenhum sócio encontrado. Criando contato para responsável:', lead.nomeResponsavel);

        const resultadoContato = await this.criarContato(lead);

        if (resultadoContato.sucesso) {
          contactId = resultadoContato.data;
          console.log('BITRIX: ✓ Contato criado com sucesso, ID:', contactId);
        } else {
          console.warn('BITRIX: ✗ Falha ao criar contato:', resultadoContato.erro);
          console.warn('BITRIX: Prosseguindo sem vinculação de contato...');
        }
      } else {
        console.log('BITRIX: Nenhum responsável definido, criando lead sem contato vinculado');
      }

      console.log('\nBITRIX: === INICIANDO CRIAÇÃO DO LEAD ===');

      // Segunda etapa: Criar lead (com ou sem vinculação de contato)
      console.log('BITRIX: Convertendo lead para formato Bitrix...');
      const payload = this.converterLeadParaBitrix(lead, contactId);

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.add.json`;

      console.log('\nBITRIX: === PAYLOAD FINAL ===');
      console.log('URL:', url);
      console.log('Payload completo:', JSON.stringify(payload, null, 2));
      console.log('Payload size (bytes):', JSON.stringify(payload).length);

      // Validar payload antes de enviar
      console.log('\nBITRIX: === VALIDAÇÃO DO PAYLOAD ===');
      this.validarPayload(payload);

      console.log('\nBITRIX: === ENVIANDO REQUISIÇÃO ===');
      const startTime = Date.now();

      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PromoKit-CRM/1.0'
        },
        timeout: 30000 // 30 segundos
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`BITRIX: Resposta recebida em ${duration}ms`);
      console.log('BITRIX: Status HTTP:', response.status);
      console.log('BITRIX: Headers de resposta:', response.headers);
      console.log('BITRIX: Resposta completa:', JSON.stringify(response.data, null, 2));

      if (response.data.error) {
        console.error('\nBITRIX: === ERRO NA RESPOSTA ===');
        console.error('Código do erro:', response.data.error);
        console.error('Descrição do erro:', response.data.error_description);
        console.error('Dados completos do erro:', response.data);

        return Resposta.erro(`Erro do Bitrix [${response.data.error}]: ${response.data.error_description}`) as any;
      }

      if (!response.data.result) {
        console.error('BITRIX: Resposta sem campo "result":', response.data);
        return Resposta.erro('Resposta inválida do Bitrix: campo "result" não encontrado') as any;
      }

      const leadId = parseInt(response.data.result);

      if (isNaN(leadId)) {
        console.error('BITRIX: ID do lead inválido:', response.data.result);
        return Resposta.erro('ID do lead retornado pelo Bitrix é inválido') as any;
      }

      console.log(`\nBITRIX: === SUCESSO ===`);
      console.log('✓ Lead criado com sucesso!');
      console.log('✓ ID do lead:', leadId);

      if (contactId) {
        console.log('✓ Lead vinculado ao contato principal ID:', contactId);
      }

      if (contatosSecundarios.length > 0) {
        console.log('✓ Contatos secundários criados:', contatosSecundarios.length, 'contatos');
        console.log('✓ IDs dos contatos secundários:', contatosSecundarios);
      }

      console.log(`====== BITRIX LEAD CREATION END [${new Date().toISOString()}] ======\n`);

      return Resposta.sucesso(leadId) as Resposta<number>;

    } catch (error) {
      console.error('\nBITRIX: === ERRO NA REQUISIÇÃO ===');
      console.error('Tipo do erro:', error.constructor.name);
      console.error('Mensagem:', error.message);

      if (error.response) {
        console.error('Status HTTP:', error.response.status);
        console.error('Headers de resposta:', error.response.headers);
        console.error('Dados da resposta:', error.response.data);
      } else if (error.request) {
        console.error('Erro de rede - Request feito mas sem resposta:', error.request);
      } else {
        console.error('Erro na configuração:', error.message);
      }

      console.error('Stack trace:', error.stack);
      console.log(`====== BITRIX LEAD CREATION ERROR END [${new Date().toISOString()}] ======\n`);

      return Resposta.erro(`Erro ao conectar com Bitrix: ${error.message}`) as any;
    }
  }

  /**
   * Converte um sócio para contato do Bitrix
   */
  private converterSocioParaContato(lead: Lead, socio: Socio): BitrixContactPayload {
    // Extrair nome e sobrenome do sócio
    const nomeCompleto = socio.nome || '';
    const partesNome = nomeCompleto.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    console.log(`BITRIX: Convertendo sócio ${socio.nome} para contato`);
    console.log('BITRIX: Nome:', nome);
    console.log('BITRIX: Sobrenome:', sobrenome);
    console.log('BITRIX: Cargo:', socio.cargo);
    console.log('BITRIX: Principal:', socio.principal);

    const fields: BitrixContactFields = {
      NAME: nome,
      LAST_NAME: sobrenome,
    };

    // Adicionar telefone preferencial se disponível
    const telefonePreferencial = this.obterTelefonePreferencial(lead);
    if (telefonePreferencial) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(telefonePreferencial),
        VALUE_TYPE: 'MOBILE'
      }];
      console.log('BITRIX: Telefone adicionado ao contato do sócio:', this.formatarTelefone(telefonePreferencial));
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
      console.log('BITRIX: Email adicionado ao contato do sócio:', lead.crmEmpresa.email);
    }

    // Comentários específicos do sócio
    const comentarios = this.montarComentariosSocio(lead, socio);
    fields.COMMENTS = comentarios;

    // Adicionar campo customizado para identificar o cargo/função
    if (socio.cargo) {
      fields.POST = socio.cargo; // Campo "Cargo" padrão do Bitrix
    }

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários específicos para um sócio
   */
  private montarComentariosSocio(lead: Lead, socio: Socio): string {
    const comentarios: string[] = [];

    // Identificação do tipo de contato
    comentarios.push(socio.principal ? 'SÓCIO PRINCIPAL' : 'SÓCIO SECUNDÁRIO');
    comentarios.push(`Empresa: ${lead.empresa}`);
    comentarios.push(`Origem do lead: ${lead.origem}`);

    // Informações do cargo
    if (socio.cargo) {
      comentarios.push(`Cargo: ${socio.cargo}`);
    }

    // Data de entrada na sociedade
    if (socio.dataEntrada) {
      comentarios.push(`Sócio desde: ${socio.dataEntrada}`);
    }

    // Score de análise se disponível
    if (socio.scoreAnalise) {
      comentarios.push(`Score de análise: ${socio.scoreAnalise}%`);
    }

    // Motivo da seleção como principal
    if (socio.motivoSelecao) {
      comentarios.push(`Motivo da análise: ${socio.motivoSelecao}`);
    }

    // Dados da empresa
    if (lead.segmento) {
      comentarios.push(`Segmento: ${lead.segmento}`);
    }

    if (lead.cnpj) {
      comentarios.push(`CNPJ: ${lead.formatarCnpj()}`);
    }

    const comentarioCompleto = comentarios.join('\\n');

    // Limitar tamanho do comentário
    const LIMITE_CARACTERES = 900;
    if (comentarioCompleto.length > LIMITE_CARACTERES) {
      console.log(`BITRIX: Comentário do sócio muito longo (${comentarioCompleto.length} chars), truncando para ${LIMITE_CARACTERES}`);
      return comentarioCompleto.substring(0, LIMITE_CARACTERES) + '\\n\\n[...TEXTO TRUNCADO...]';
    }

    return comentarioCompleto;
  }

  /**
   * Converte um Lead do nosso sistema para contato do Bitrix
   */
  private converterLeadParaContato(lead: Lead): BitrixContactPayload {
    // Extrair nome e sobrenome
    const nomeResponsavel = lead.nomeResponsavel || '';
    const partesNome = nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    console.log('BITRIX: Convertendo lead para contato');
    console.log('BITRIX: Nome completo:', nomeResponsavel);
    console.log('BITRIX: Nome:', nome);
    console.log('BITRIX: Sobrenome:', sobrenome);

    const fields: BitrixContactFields = {
      NAME: nome,
      LAST_NAME: sobrenome,
    };

    // Adicionar telefone preferencial se disponível
    const telefonePreferencial = this.obterTelefonePreferencial(lead);
    if (telefonePreferencial) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(telefonePreferencial),
        VALUE_TYPE: 'MOBILE'
      }];
      console.log('BITRIX: Telefone adicionado ao contato:', this.formatarTelefone(telefonePreferencial));
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
      console.log('BITRIX: Email adicionado ao contato:', lead.crmEmpresa.email);
    }

    // Comentários básicos sobre o contato
    const comentarios = `Responsável da empresa: ${lead.empresa}\nOrigem: ${lead.origem}`;
    fields.COMMENTS = comentarios;

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Converte um Lead do nosso sistema para o formato do Bitrix
   */
  private converterLeadParaBitrix(lead: Lead, contactId?: number): BitrixLeadPayload {
    // Extrair nome e sobrenome
    const nomeResponsavel = lead.nomeResponsavel || '';
    const partesNome = nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    // Montar comentários com informações extras
    const comentarios = this.montarComentarios(lead);

    // Determinar fonte baseada na origem
    const fonte = this.mapearOrigem(lead.origem);

    const empresa = lead.empresa || 'Empresa sem nome';
    const titulo = `${empresa} - ${nomeResponsavel}`;

    const fields: BitrixLeadFields = {
      TITLE: titulo,
      NAME: nome,
      LAST_NAME: sobrenome,
      COMPANY_TITLE: empresa,
      STATUS_ID: this.mapearEtapaParaStatus(lead.etapa),
      SOURCE_ID: fonte.id,
      SOURCE_DESCRIPTION: fonte.descricao,
      COMMENTS: comentarios
    };

    // Vincular contato se foi criado
    if (contactId) {
      fields.CONTACT_ID = contactId;
      console.log('BITRIX: Vinculando lead ao contato ID:', contactId);
    }

    // Adicionar telefone se disponível
    if (lead.telefone) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(lead.telefone),
        VALUE_TYPE: 'MOBILE'
      }];
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
    }

    // Adicionar links do Instagram e site
    const webs: BitrixWeb[] = [];

    if (lead.instagramHandle) {
      webs.push({
        VALUE: `https://instagram.com/${lead.instagramHandle}`,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.linkInsta && lead.linkInsta !== 'null' && lead.linkInsta.trim()) {
      webs.push({
        VALUE: lead.linkInsta,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.instagramData?.website && lead.instagramData.website !== 'null' && lead.instagramData.website.trim()) {
      webs.push({
        VALUE: lead.instagramData.website,
        VALUE_TYPE: 'WORK'
      });
    }

    if (webs.length > 0) {
      fields.WEB = webs;
    }

    // *** NOVO: Mapear custom fields dinamicamente ***
    console.log('BITRIX: Iniciando mapeamento dinâmico de custom fields...');
    const customFields = this.mapLeadToCustomFields(lead);

    // Adicionar todos os custom fields mapeados ao payload
    Object.assign(fields, customFields);

    // Manter campos legados para compatibilidade (serão sobrescritos se mapeados dinamicamente)
    if (!customFields['UF_CRM_1615222177542'] && lead.instagramHandle) {
      fields['UF_CRM_1615222177542'] = `https://instagram.com/${lead.instagramHandle}`;
    }

    if (lead.score !== undefined && lead.score !== null) {
      fields['UF_SCORE'] = lead.score.toString();
    }

    if (lead.segmento) {
      fields['UF_SEGMENTO'] = lead.segmento;
    }

    if (lead.instagramData?.followers) {
      fields['UF_SEGUIDORES'] = lead.instagramData.followers.toString();
    }

    console.log('BITRIX: Custom fields finais adicionados:', Object.keys(customFields).length, 'campos');
    console.log('BITRIX: Preview dos custom fields:', customFields);

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários com informações detalhadas do lead
   */
  private montarComentarios(lead: Lead): string {
    const comentarios: string[] = [];

    // Informações básicas
    comentarios.push(`Score: ${lead.score}%`);
    comentarios.push(`Segmento: ${lead.segmento || 'Não definido'}`);
    comentarios.push(`Etapa: ${lead.etapa}`);

    // Dados do Instagram (apenas métricas básicas)
    if (lead.instagramData) {
      comentarios.push('\\n=== DADOS INSTAGRAM ===');
      if (lead.instagramData.followers) {
        comentarios.push(`Seguidores: ${lead.instagramData.followers.toLocaleString()}`);
      }
      if (lead.instagramData.following) {
        comentarios.push(`Seguindo: ${lead.instagramData.following.toLocaleString()}`);
      }
      if (lead.instagramData.accountType) {
        comentarios.push(`Tipo de conta: ${lead.instagramData.accountType}`);
      }
      if (lead.instagramData.businessCategory) {
        comentarios.push(`Categoria: ${lead.instagramData.businessCategory}`);
      }
    }

    // Observações
    if (lead.observacoes) {
      comentarios.push('\\n=== OBSERVAÇÕES DE VENDAS ===');
      comentarios.push(lead.observacoes);
    }

    const comentarioCompleto = comentarios.join('\\n');

    // Limitar tamanho do comentário para evitar erro 400 do Bitrix
    const LIMITE_CARACTERES = 900; // Limite conservador (Bitrix aceita máximo 1000)
    if (comentarioCompleto.length > LIMITE_CARACTERES) {
      console.log(`BITRIX: Comentário muito longo (${comentarioCompleto.length} chars), truncando para ${LIMITE_CARACTERES}`);
      return comentarioCompleto.substring(0, LIMITE_CARACTERES) + '\\n\\n[...TEXTO TRUNCADO...]';
    }

    return comentarioCompleto;
  }

  /**
   * Mapeia origem do lead para fonte do Bitrix
   */
  private mapearOrigem(origem: string): { id: string; descricao: string } {
    const mapeamento: Record<string, { id: string; descricao: string }> = {
      'Instagram': { id: 'WEB', descricao: 'Lead gerado via Instagram' },
      'Site/Landing Page': { id: 'WEB', descricao: 'Lead do site/landing page' },
      'WhatsApp Direto': { id: 'OTHER', descricao: 'Contato direto via WhatsApp' },
      'Indicação': { id: 'PARTNER', descricao: 'Lead por indicação' },
      'Evento/Feira': { id: 'TRADE_SHOW', descricao: 'Lead de evento/feira' },
      'Outros': { id: 'OTHER', descricao: 'Outras fontes' }
    };

    return mapeamento[origem] || { id: 'OTHER', descricao: origem };
  }

  /**
   * Mapeia etapa do funil para status do Bitrix
   */
  private mapearEtapaParaStatus(etapa: string): string {
    const mapeamento: Record<string, string> = {
      'Prospecção': 'NEW',
      'Qualificação': 'IN_PROCESS',
      'Objeção': 'IN_PROCESS',
      'Fechamento': 'PROCESSED',
      'Ganho': 'CONVERTED',
      'Perdido': 'JUNK'
    };

    return mapeamento[etapa] || 'NEW';
  }

  /**
   * Obter telefone preferencial do lead (WhatsApp > Celular > Principal)
   */
  private obterTelefonePreferencial(lead: Lead): string | null {
    // Prioridade 1: WhatsApp
    const telefoneWhatsApp = lead.getTelefoneWhatsApp?.();
    if (telefoneWhatsApp?.numero) {
      console.log('BITRIX: Usando telefone WhatsApp:', telefoneWhatsApp.numero);
      return telefoneWhatsApp.numero;
    }

    // Prioridade 2: Celular
    const telefoneCelular = lead.getTelefoneCelular?.();
    if (telefoneCelular?.numero) {
      console.log('BITRIX: Usando telefone celular:', telefoneCelular.numero);
      return telefoneCelular.numero;
    }

    // Prioridade 3: Telefone principal
    if (lead.telefone) {
      console.log('BITRIX: Usando telefone principal:', lead.telefone);
      return lead.telefone;
    }

    console.warn('BITRIX: Nenhum telefone encontrado no lead');
    return null;
  }

  /**
   * Formata telefone para o padrão internacional
   */
  private formatarTelefone(telefone: string): string {
    const numeroLimpo = telefone.replace(/\D/g, '');

    if (numeroLimpo.startsWith('55')) {
      return `+${numeroLimpo}`;
    }

    return `+55${numeroLimpo}`;
  }

  /**
   * Busca um lead no Bitrix pelo ID
   */
  async buscarLead(leadId: number): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.get.json?id=${leadId}`;

      const response = await axios.get(url);

      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      return Resposta.sucesso(response.data.result) as Resposta<any>;
    } catch (error) {
      return Resposta.erro(`Erro ao buscar lead: ${error.message}`);
    }
  }

  /**
   * Atualiza um lead no Bitrix
   */
  async atualizarLead(leadId: number, campos: Partial<BitrixLeadFields>): Promise<Resposta<boolean>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.update.json`;

      const payload = {
        id: leadId,
        fields: campos
      };

      const response = await axios.post(url, payload);

      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      return Resposta.sucesso(true) as Resposta<boolean>;
    } catch (error) {
      return Resposta.erro(`Erro ao atualizar lead: ${error.message}`) as any;
    }
  }

  /**
   * Lista os custom fields (campos personalizados) de leads do Bitrix
   */
  async listarCustomFields(): Promise<Resposta<any[]>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.list.json`;

      console.log('BITRIX: Listando custom fields de leads...');
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);

      if (response.data.error) {
        console.error('BITRIX: Erro ao listar custom fields:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const customFields = response.data.result;
      console.log('BITRIX: Custom fields raw data:', JSON.stringify(customFields, null, 2));
      console.log('BITRIX: Custom fields encontrados:', customFields.length);

      // Processar campos para exibir informações mais úteis
      const camposProcessados = customFields.map((field: any) => ({
        id: field.ID,
        fieldName: field.FIELD_NAME,
        userTypeId: field.USER_TYPE_ID,
        listLabel: field.LIST_LABEL,
        listColumnLabel: field.LIST_COLUMN_LABEL,
        listFilterLabel: field.LIST_FILTER_LABEL,
        editFormLabel: field.EDIT_FORM_LABEL,
        settings: field.SETTINGS,
        mandatory: field.MANDATORY === 'Y',
        multiple: field.MULTIPLE === 'Y',
        sort: field.SORT,
        showInList: field.SHOW_IN_LIST === 'Y',
        editInList: field.EDIT_IN_LIST === 'Y',
        showFilter: field.SHOW_FILTER === 'Y'
      }));

      console.log('BITRIX: Campos processados:', JSON.stringify(camposProcessados, null, 2));

      return Resposta.sucesso(camposProcessados) as Resposta<any[]>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição de custom fields:', error);
      return Resposta.erro(`Erro ao listar custom fields: ${error.message}`) as any;
    }
  }

  /**
   * Busca um custom field específico do Bitrix pelo ID
   */
  async buscarCustomField(fieldId: string): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.get.json?id=${fieldId}`;

      console.log('BITRIX: Buscando custom field ID:', fieldId);
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar custom field:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      const customField = response.data.result;
      console.log('BITRIX: Custom field raw data:', JSON.stringify(customField, null, 2));

      if (!customField) {
        return Resposta.erro(`Custom field com ID ${fieldId} não encontrado`);
      }

      // Processar campo para exibir informações mais úteis
      const campoProcessado = {
        id: customField.ID,
        fieldName: customField.FIELD_NAME,
        userTypeId: customField.USER_TYPE_ID,
        xmlId: customField.XML_ID,
        listLabel: customField.LIST_LABEL,
        listColumnLabel: customField.LIST_COLUMN_LABEL,
        listFilterLabel: customField.LIST_FILTER_LABEL,
        editFormLabel: customField.EDIT_FORM_LABEL,
        errorMessage: customField.ERROR_MESSAGE,
        helpMessage: customField.HELP_MESSAGE,
        settings: customField.SETTINGS,
        mandatory: customField.MANDATORY === 'Y',
        multiple: customField.MULTIPLE === 'Y',
        sort: customField.SORT,
        showInList: customField.SHOW_IN_LIST === 'Y',
        editInList: customField.EDIT_IN_LIST === 'Y',
        showFilter: customField.SHOW_FILTER === 'Y',
        showAdvancedFilter: customField.SHOW_ADV_FILTER === 'Y',
        entityId: customField.ENTITY_ID,
        dateCreate: customField.DATE_CREATE,
        dateModify: customField.DATE_MODIFY,
        createdBy: customField.CREATED_BY,
        modifiedBy: customField.MODIFIED_BY
      };

      console.log('BITRIX: Campo processado:', JSON.stringify(campoProcessado, null, 2));

      return Resposta.sucesso(campoProcessado) as Resposta<any>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição do custom field:', error);
      return Resposta.erro(`Erro ao buscar custom field: ${error.message}`);
    }
  }

  /**
   * Lista custom fields com detalhes completos (combina list + get para cada campo)
   */
  async listarCustomFieldsCompletos(): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando custom fields completos...');

      // Primeiro, listar todos os custom fields
      const resultadoLista = await this.listarCustomFields();

      if (!resultadoLista.sucesso) {
        return resultadoLista;
      }

      const customFields = resultadoLista.data;
      console.log('BITRIX: Obtendo detalhes para', customFields.length, 'custom fields...');

      const camposCompletos = [];

      // Para cada campo, buscar os detalhes completos
      for (const campo of customFields) {
        try {
          console.log('BITRIX: Buscando detalhes do campo ID:', campo.id);

          const resultadoDetalhe = await this.buscarCustomField(campo.id);

          if (resultadoDetalhe.sucesso) {
            // Combinar dados da lista com detalhes completos
            const campoCompleto = {
              id: campo.id,
              fieldName: campo.fieldName,
              userTypeId: campo.userTypeId,
              label: this.extrairLabelPortugues(resultadoDetalhe.data.listColumnLabel) ||
                     this.extrairLabelPortugues(resultadoDetalhe.data.editFormLabel) ||
                     this.extrairLabelPortugues(resultadoDetalhe.data.listFilterLabel) ||
                     campo.fieldName,
              mandatory: resultadoDetalhe.data.mandatory,
              multiple: resultadoDetalhe.data.multiple,
              showInList: resultadoDetalhe.data.showInList,
              showFilter: resultadoDetalhe.data.showFilter,
              sort: resultadoDetalhe.data.sort,
              settings: resultadoDetalhe.data.settings
            };

            camposCompletos.push(campoCompleto);
          } else {
            console.warn('BITRIX: Erro ao buscar detalhes do campo', campo.id, ':', resultadoDetalhe.erro);
            // Manter o campo mesmo sem detalhes completos
            camposCompletos.push(campo);
          }

          // Pequena pausa para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error('BITRIX: Erro ao processar campo', campo.id, ':', error);
          // Manter o campo mesmo com erro
          camposCompletos.push(campo);
        }
      }

      console.log('BITRIX: Custom fields completos processados:', camposCompletos.length);

      return Resposta.sucesso(camposCompletos) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar custom fields completos:', error);
      return Resposta.erro(`Erro ao buscar custom fields completos: ${error.message}`) as any;
    }
  }

  /**
   * Extrai label em português de um objeto de labels multilíngue
   */
  private extrairLabelPortugues(labelObj: any): string {
    if (!labelObj) return '';

    if (typeof labelObj === 'string') {
      return labelObj;
    }

    if (typeof labelObj === 'object') {
      // Tentar português brasileiro primeiro, depois português, depois qualquer chave
      return labelObj.br || labelObj.pt || labelObj.en || labelObj[Object.keys(labelObj)[0]] || '';
    }

    return String(labelObj);
  }

  /**
   * Retorna o mapeamento estático dos custom fields (não precisa buscar do Bitrix)
   */
  private getCustomFieldsMapping(): Map<string, string> {
    if (this.customFieldsMappingCache.size === 0) {
      // Criar cache baseado na configuração estática
      this.CUSTOM_FIELD_MAPPINGS.forEach(mapping => {
        this.customFieldsMappingCache.set(mapping.label, mapping.fieldName);
      });
      console.log('BITRIX: Cache de mapeamento criado com', this.customFieldsMappingCache.size, 'campos');
    }

    return this.customFieldsMappingCache;
  }

  /**
   * Obtém valor de uma propriedade aninhada do objeto Lead ou chama método
   */
  private getNestedProperty(obj: any, path: string): any {
    // Se o path é um método (não contém ponto), tenta chamar como método
    if (!path.includes('.') && typeof obj[path] === 'function') {
      try {
        return obj[path]();
      } catch (error) {
        console.warn(`BITRIX: Erro ao chamar método ${path}:`, error);
        return undefined;
      }
    }

    // Caso contrário, navega pelas propriedades aninhadas
    return path.split('.').reduce((current, prop) => {
      return current && current[prop] !== undefined ? current[prop] : undefined;
    }, obj);
  }

  /**
   * Mapeia dados do Lead para custom fields do Bitrix
   */
  private mapLeadToCustomFields(lead: Lead): Record<string, any> {
    const customFields: Record<string, any> = {};

    // Buscar mapeamento dos custom fields (agora é síncrono)
    this.getCustomFieldsMapping();

    console.log('BITRIX: Iniciando mapeamento de custom fields para lead:', lead.nomeResponsavel);
    console.log('BITRIX: Lead possui', lead.links?.length || 0, 'links associados');

    // Log detalhado dos links se existirem
    if (lead.links && lead.links.length > 0) {
      console.log('BITRIX: Links detalhados:');
      lead.links.forEach((link: any, index: number) => {
        console.log(`BITRIX: Link ${index + 1}: ${link.tipo} = ${link.url} (ativo: ${link.ativo})`);
      });
    }

    // Testar métodos do Lead
    console.log('BITRIX: Testando métodos do Lead:');
    if (typeof lead.getLinkInstagram === 'function') {
      console.log('BITRIX: getLinkInstagram():', lead.getLinkInstagram());
    } else {
      console.log('BITRIX: getLinkInstagram() não é uma função');
    }

    if (typeof lead.getLinkIfood === 'function') {
      console.log('BITRIX: getLinkIfood():', lead.getLinkIfood());
    } else {
      console.log('BITRIX: getLinkIfood() não é uma função');
    }

    if (typeof lead.getLinkConcorrente === 'function') {
      console.log('BITRIX: getLinkConcorrente():', lead.getLinkConcorrente());
    } else {
      console.log('BITRIX: getLinkConcorrente() não é uma função');
    }

    // Processar cada mapeamento configurado
    for (const mapping of this.CUSTOM_FIELD_MAPPINGS) {
      if (!mapping.fieldName) {
        console.warn(`BITRIX: FieldName não encontrado para "${mapping.label}"`);
        continue;
      }

      // Obter valor da propriedade do lead
      let value = this.getNestedProperty(lead, mapping.leadProperty);

      console.log(`BITRIX: Tentando mapear ${mapping.label} (${mapping.leadProperty}): ${value}`);

      if (value !== undefined && value !== null && value !== '') {
        // Aplicar transformação se definida
        if (mapping.transform) {
          const originalValue = value;
          value = mapping.transform(value);
          console.log(`BITRIX: Transformação aplicada para ${mapping.label}: ${originalValue} -> ${value}`);
        }

        // Validação extra para evitar valores problemáticos
        if (Array.isArray(value)) {
          // Filtrar valores inválidos de arrays
          value = value.filter(item => item && item !== 'null' && String(item).trim() !== '');
          if (value.length === 0) {
            console.log(`BITRIX: ✗ Array vazio após filtrar valores inválidos para ${mapping.label}`);
            continue;
          }
        } else if (String(value) === 'null' || String(value).trim() === '') {
          console.log(`BITRIX: ✗ Valor inválido ('null' ou vazio) para ${mapping.label}`);
          continue;
        }

        customFields[mapping.fieldName] = value;
        console.log(`BITRIX: ✓ Custom field mapeado: ${mapping.fieldName} (${mapping.label}) = ${JSON.stringify(value)}`);
      } else {
        console.log(`BITRIX: ✗ Valor vazio para ${mapping.label} (${mapping.leadProperty})`);
      }
    }

    console.log('BITRIX: Total de custom fields mapeados:', Object.keys(customFields).length);
    console.log('BITRIX: Resumo dos campos enviados:', Object.keys(customFields).map(key => {
      const mapping = this.CUSTOM_FIELD_MAPPINGS.find(m => m.fieldName === key);
      return `${key} (${mapping?.label || 'Desconhecido'}): ${customFields[key]}`;
    }));

    return customFields;
  }
}

/**
 * Factory para criar instância do BitrixService com configuração padrão
 */
export class BitrixServiceFactory {
  static criarInstancia(): BitrixService {
    const config: BitrixConfig = {
      baseUrl: 'https://b24-chlbsw.bitrix24.com.br',
      userId: '1',
      webhook: '19i08a5m1x8am1f6'
    };

    return new BitrixService(config);
  }

  static criarInstanciaPersonalizada(baseUrl: string, userId: string, webhook: string): BitrixService {
    const config: BitrixConfig = {
      baseUrl,
      userId,
      webhook
    };

    return new BitrixService(config);
  }
}
