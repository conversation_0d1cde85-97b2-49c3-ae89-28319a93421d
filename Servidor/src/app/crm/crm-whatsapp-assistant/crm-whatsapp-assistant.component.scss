// WhatsApp Assistant - Design Limpo e Funcional
// Estilos simples e profissionais

// ===== ESTILOS GERAIS =====
.container-fluid {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

// ===== CARDS LIMPOS =====
.card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 15px 20px;

    h5 {
      margin: 0;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 20px;
  }
}

// ===== BOTÕES SPIN =====
.btn-group {
  .btn {
    border-radius: 6px;
    margin: 2px;
    padding: 10px 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: 2px solid #e9ecef;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    i {
      font-size: 16px;
      margin-bottom: 4px;
    }

    small {
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}

// ===== ESTADOS DOS BOTÕES =====
.btn-primary, .badge-primary {
  background-color: #6c5ce7 !important;
  border-color: #6c5ce7 !important;
  color: white !important;
}

.btn-outline-primary, .badge-light {
  background-color: white !important;
  border-color: #6c5ce7 !important;
  color: #6c5ce7 !important;

  &:hover {
    background-color: #6c5ce7 !important;
    color: white !important;
  }
}

// ===== FORM CONTROLS =====
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;

  &:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
  }
}

// ===== BOTÕES =====
.btn {
  border-radius: 6px;
  font-weight: 600;

  &:hover {
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
  }
}

// ===== ANIMAÇÕES =====
@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.rotating {
  animation: rotating 1s linear infinite;
}

// ===== RESPONSIVIDADE =====
@media (max-width: 768px) {
  .container-fluid {
    padding: 10px;
  }

  .btn-group .btn {
    font-size: 12px;
    padding: 8px 6px;
  }
}
