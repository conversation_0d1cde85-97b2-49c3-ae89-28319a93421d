-- Script para adicionar colunas de fase SPIN detectada pela IA na tabela crm_lead

-- Adicionar coluna para armazenar a fase SPIN detectada automaticamente
ALTER TABLE crm_lead ADD COLUMN fase_spin_detectada varchar(32) NULL COMMENT 'Fase SPIN detectada automaticamente pela IA (rapport, situacao, problema, implicacao, necessidade)';

-- Adicionar coluna para armazenar a confiança da detecção
ALTER TABLE crm_lead ADD COLUMN confianca_fase_detectada decimal(3,2) NULL COMMENT 'Confiança da detecção da fase SPIN (0.00 a 1.00)';

-- Adicionar coluna para armazenar quando foi feita a última detecção
ALTER TABLE crm_lead ADD COLUMN data_ultima_deteccao_fase datetime NULL COMMENT 'Data e hora da última detecção automática de fase SPIN';

-- Verificar estrutura da tabela após as alterações
-- SHOW COLUMNS FROM crm_lead;
